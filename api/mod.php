<?php
require __DIR__.'/../cronometrajeinstantaneo.env';
require __DIR__.'/../funciones.php';
require __DIR__.'/../vendor/autoload.php';

$idlectura = filter_input(INPUT_POST, 'idlectura', FILTER_VALIDATE_INT);
$tiempo = filter_input(INPUT_POST, 'tiempo', FILTER_SANITIZE_SPECIAL_CHARS);
$idparticipante = filter_input(INPUT_POST, 'idparticipante', FILTER_VALIDATE_INT);
$codigo = filter_input(INPUT_POST, 'codigo', FILTER_SANITIZE_SPECIAL_CHARS);

$bd_link = conectar_db(BD_BD, BD_HOST, BD_USER, BD_PASS);
$mobile = session_mobile($codigo);

if (!$idlectura) {
    header("HTTP/1.1 400 Bad Request");
    echo json_encode([
        'error'     => true,
        'resultado' => "Error en idlectura"]);
    exit();
}

if (!$tiempo) {
    header("HTTP/1.1 400 Bad Request");
    echo json_encode([
        'error'     => true,
        'resultado' => "Error en tiempo"]);
    exit();
}

$resultado = array();
if ($mobile['largada'] == '0000-00-00 00:00:00.000') {
    $resultado['error'] = true;
    $resultado['resultado'] = 'EVENTO SIN COMENZAR';
    log_api('Lectura; NO EMPEZÓ - '.$idparticipante.' ('.$nombre.')');

} elseif ($mobile['terminada'] != '0000-00-00 00:00:00.000') {
    $resultado['error'] = true;
    $resultado['resultado'] = 'EVENTO TERMINADO';
    log_api('Lectura; EVENTO TERMINADO - '.$idparticipante.' ('.$nombre.')');

} else {

    $lectura = array_sql(consulta_sql(
        "SELECT *
        FROM lecturas
        WHERE idlectura = '".$idlectura."'
            AND idcontrol = '".$mobile['idcontrol']."'
        LIMIT 1"));


    // PARCHE PARA BAJA PEDERNALES
    if (in_array($mobile['idcontrol'], [7326, 7327])) {
        $lectura = array_sql(consulta_sql(
            "SELECT *
            FROM lecturas
            WHERE idlectura = '".$idlectura."'
            LIMIT 1"));
        $mobile['idcontrol'] = $lectura['idcontrol'];
    }
    // PARCHE PARA BAJA PEDERNALES


    consulta_sql(
        "UPDATE lecturas SET
            tiempo = '".$tiempo."',
            idparticipante = '".$idparticipante."'
        WHERE idlectura = '".$idlectura."'
            AND idcontrol = '".$mobile['idcontrol']."'
        LIMIT 1");
    if (afectado_sql())
        cache($mobile['evento_codigo']);

    $mostrar = explode(' ', $tiempo);
    $tiempo_listo = convertir_final(segundos_transcurridos($mobile['largada'], $tiempo), 'milisegundos');

    if ($idparticipante) {
        $resultado_sql = consulta_sql(
            "SELECT idinscripcion, nombre,
                (SELECT idlectura FROM lecturas
                WHERE idparticipante = '{$idparticipante}'
                    AND idcontrol = '{$mobile['idcontrol']}'
                    AND idlectura < '{$idlectura}' LIMIT 1) AS lectura_anterior
            FROM participantes
            WHERE idparticipante = '".$idparticipante."'
                AND idevento = '".$mobile['idevento']."'
                AND participantes.estado != 'eliminado'
            LIMIT 1");
        $participante = contar_sql($resultado_sql)
            ? array_sql($resultado_sql)
            : [];

    } else {
        $participante = false;
        $idparticipante = 'FALTA';
    }

    $resultado['tiempo'] = $tiempo;
    $resultado['hora'] = $mostrar[1];
    $resultado['tiempo_listo'] = $tiempo_listo;
    $resultado['idparticipante'] = $idparticipante;
    $resultado['idlectura'] = $idlectura;

    if (!$idparticipante) {
        $resultado['resultado'] = 'NO existe';
        log_api('Mod; NO existe el participante '.$idparticipante.' ('.$tiempo_listo.' - '.$participante['nombre'].')');
    } elseif ($idparticipante && !$participante['idinscripcion']) {
        $resultado['resultado'] = 'NO existe';
        log_api('Mod; NO existe el participante '.$idparticipante.' ('.$tiempo_listo.' - '.$participante['nombre'].')');
    } elseif ($idparticipante && $participante['lectura_anterior']) {
        $resultado['resultado'] = 'NO repetido';
        $resultado['nombre'] = $participante['nombre'] = $participante['nombre'];
        log_api('Mod; NO repetido el participante '.$idparticipante.' ('.$tiempo_listo.' - '.$participante['nombre'].')');
    } else {
        $resultado['resultado'] = 'OK';
        $resultado['nombre'] = $participante['nombre'] = $participante['nombre'];
        log_api('Lectura; OK '.$idparticipante.' ('.$tiempo_listo.' - '.$participante['nombre'].')');
    }

    if (!$idparticipante)
        $resultado['idparticipante'] = 'FALTA';

    if ($participante['idinscripcion']
        && contar_sql(consulta_sql(
        "SELECT iddato
        FROM `datosxeventos`
        WHERE apps = 1
            AND idevento = '{$mobile['idevento']}'
        LIMIT 1"))) {
        $resultado['extra'] = campo_sql(consulta_sql(
            "SELECT dato
            FROM datosxparticipantes
            WHERE idinscripcion = '{$participante['idinscripcion']}'
                AND iddato = (SELECT iddato FROM datosxeventos
                    WHERE apps = 1 AND idevento = '{$mobile['idevento']}' LIMIT 1)"
        ), 0, 'dato');
    } else {
        $resultado['extra'] = '';
    }

    log_api('Mod; OK Lectura '.$idlectura.' (Datos nuevos: tiempo='.$tiempo.', idparticipante='.$idparticipante.') (Datos viejos:  tiempo='.$lectura['tiempo'].', idparticipante='.$lectura['idparticipante'].')');

    if ($idparticipante && in_array($mobile['tipo'], ['largada', 'final']) && $mobile['streaming']) {

        $url = URL_API.'lecturas';
        $ch = curl_init( $url );
        # Setup request to send json via POST.
        $payload = json_encode([
            'idlectura' => $resultado['idlectura'],
        ]);
        curl_setopt( $ch, CURLOPT_POSTFIELDS, $payload );
        curl_setopt( $ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json'));
        # Return response instead of printing.
        curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
        # Send request.
        $result = curl_exec($ch);
        curl_close($ch);
        $resultado['refactor'] = $result;

    }

}
echo json_encode($resultado);
