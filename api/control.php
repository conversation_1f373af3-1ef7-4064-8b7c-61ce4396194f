<?php
require __DIR__.'/../cronometrajeinstantaneo.env';
require __DIR__.'/../funciones.php';
require __DIR__.'/../vendor/autoload.php';

// use Google\Cloud\Firestore\FirestoreClient;

$codigo = filter_input(INPUT_POST, 'codigo', FILTER_SANITIZE_SPECIAL_CHARS);

$bd_link = conectar_db(BD_BD, BD_HOST, BD_USER, BD_PASS);
$mobile = session_mobile($codigo);

if ($mobile['terminada'] != '0000-00-00 00:00:00.000') {
    header("HTTP/1.1 400 Bad Request");
    $resultado['error'] = true;
    $resultado['resultado'] = 'No se cambio el código. El evento esta terminado';
}

$resultado['codigo'] = $mobile['codigo'];
$resultado['evento'] = $mobile['evento'];
$resultado['nombre'] = $mobile['nombre'];
$resultado['tipo'] = $mobile['tipo'];
$resultado['limitar_penas'] = $mobile['limitar_penas'];

echo json_encode($resultado);
