<?php
require __DIR__.'/../cronometrajeinstantaneo.env';
require __DIR__.'/../funciones.php';
require __DIR__.'/../vendor/autoload.php';

// use Google\Cloud\Firestore\FirestoreClient;

$tiempo = filter_input(INPUT_POST, 'tiempo', FILTER_SANITIZE_SPECIAL_CHARS);
$idparticipante = intval(filter_input(INPUT_POST, 'idparticipante', FILTER_SANITIZE_SPECIAL_CHARS));
$codigo = filter_input(INPUT_POST, 'codigo', FILTER_SANITIZE_SPECIAL_CHARS);
$tagID = filter_input(INPUT_POST, 'tagID', FILTER_SANITIZE_SPECIAL_CHARS);
$cronometrador = filter_input(INPUT_POST, 'cronometrador', FILTER_SANITIZE_SPECIAL_CHARS);

$bd_link = conectar_db(BD_BD, BD_HOST, BD_USER, BD_PASS);
$mobile = session_mobile($codigo);

if (!$tiempo) {
    header("HTTP/1.1 400 Bad Request");
    echo json_encode([
        'error'     => true,
        'resultado' => "Error en el tiempo"]);
    exit();
}

// PRUEBAS PARA FIRESTORE
// if (in_array($mobile['idevento'], [])) {
//     $db = new FirestoreClient([
//         'keyFilePath' => GOOGLE_CLOUD_KEY_FILE,
//         'projectId' => GOOGLE_CLOUD_PROJECT_ID,
//     ]);

//     $usersRef = $db->collection('environments');
//     $snapshot = $usersRef->documents();
//     echo json_encode($snapshot);
//     exit();
// }

$resultado = array();
if ($mobile['largada'] == '0000-00-00 00:00:00.000') {
    header("HTTP/1.1 400 Bad Request");
    $resultado['error'] = true;
    $resultado['resultado'] = 'EVENTO SIN COMENZAR';
    log_api('Lectura; NO EMPEZÓ - '.$idparticipante);

} elseif ($mobile['terminada'] != '0000-00-00 00:00:00.000') {
    header("HTTP/1.1 400 Bad Request");
    $resultado['error'] = true;
    $resultado['resultado'] = 'EVENTO TERMINADO';
    log_api('Lectura; EVENTO TERMINADO - '.$idparticipante);

} else {

    $mostrar = explode(' ', $tiempo);
    $tiempo_listo = convertir_final(segundos_transcurridos($mobile['largada'], $tiempo), 'milisegundos');

    if (!$idparticipante && $tagID) {
        if ($mobile['tags_descartables']) {
            // El tagID es un número entero pero con varios ceros adelante
            $resultado_sql = consulta_sql(
                "SELECT idparticipante
                FROM participantes
                WHERE idevento = '{$mobile['idevento']}'
                    AND tag = '".intval($tagID)."'
                LIMIT 1");
        } else {
            $resultado_sql = consulta_sql(
                "SELECT idparticipante
                FROM participantes
                WHERE idinscripcion =
                    (SELECT idinscripcion FROM tags
                        WHERE tagID = '{$tagID}'
                        AND idevento = '{$mobile['idevento']}'
                    LIMIT 1)");
        }
        $idparticipante = contar_sql($resultado_sql)
            ? campo_sql($resultado_sql, 0, 'idparticipante')
            : 0;
    }

    if ($idparticipante) {
        $resultado_sql = consulta_sql(
            "SELECT idinscripcion, participantes.nombre, participantes.apellido, categorias.nombre AS categoria
            FROM participantes
                LEFT JOIN categorias ON participantes.idcategoria = categorias.idcategoria
            WHERE idparticipante > 0
                AND idparticipante = '$idparticipante'
                AND participantes.idevento = '{$mobile['idevento']}'
                AND participantes.estado != 'eliminado'
            LIMIT 1");
        $participante = contar_sql($resultado_sql)
            ? array_sql($resultado_sql)
            : ['nombre' => '', 'apellido' => ''];
    } else {
        $participante = [
            'nombre' => '',
            'apellido' => '',
        ];
    }

    $cant_vueltas = 0;
    $cant_repetido = 0;

    if ($mobile['tipo'] == 'acreditar' && $idparticipante) {
        consulta_sql(
            "UPDATE participantes SET estado = 'acreditado'
            WHERE idevento = '{$mobile['idevento']}'
                AND estado != 'eliminado'
                AND idparticipante = '$idparticipante'
            LIMIT 1
            ");

    } else {

        // Deshabilitado temporalmente el control de vueltas
        if ($idparticipante && in_array($mobile['idcontrol'], [9710, 9711])) {

            $lecturas_sql = consulta_sql(
                "SELECT idlectura, tiempo
                FROM lecturas
                WHERE idcontrol = '".$mobile['idcontrol']."'
                    AND estado != 'eliminado'
                    AND idparticipante = '".$idparticipante."'
                ORDER BY tiempo");

            $ultima_vuelta = 0;
            while ($lectura = array_sql($lecturas_sql)) {

                if (strtotime($lectura['tiempo']) - $ultima_vuelta < 30) {
                    $cant_repetido++;

                } else {
                    $cant_vueltas++;
                    $cant_repetido = 0;
                    $ultima_vuelta = strtotime($lectura['tiempo']);
                }
            }

            if (strtotime($tiempo) - $ultima_vuelta < 30) {
                $cant_repetido++;

            } else {
                $cant_vueltas++;
                $cant_repetido = 0;
            }
        }


// PARCHE PARA MISMO CONTROL PARA LARGADA Y LLEGADA
if ($idparticipante && in_array($mobile['idcontrol'], [8])) {

    $existe = contar_sql(consulta_sql("
        SELECT tiempo
        FROM lecturas
        WHERE idparticipante = '".$idparticipante."'
        AND (estado = 'ok' OR estado = 'repetido')
        AND idcontrol = '".$mobile['idcontrol']."'"));

    if ($existe)
        $mobile['idcontrol'] = $mobile['idcontrol']+1;

    // $tiempo_lectura2 = campo_sql(consulta_sql("
    //     SELECT tiempo
    //     FROM lecturas
    //     WHERE idparticipante = '".$idparticipante."'
    //     AND idcontrol = '".$mobile['idcontrol']."'"), 0, 'tiempo');
    // $segundos_transcurridos_2 = segundos_transcurridos($tiempo_lectura2, $tiempo);
    // if ($segundos_transcurridos_2 < 86400 && $segundos_transcurridos_2 > 5) {
    //     $mobile['idcontrol'] = $mobile['idcontrol']+1;
    // }
}
// PARCHE PARA MISMO CONTROL PARA LARGADA Y LLEGADA

        // PARCHE MISMO CONTROL ESPECÍFICO
        $temp_cambio_control = [
            17867 => 17869,
            17863 => 17865,
            17865 => 17880,
        ];
        if ($idparticipante
            && in_array($mobile['idevento'], [2456])
            && key_exists($mobile['idcontrol'], $temp_cambio_control)) {
            while (contar_sql(consulta_sql("
                SELECT tiempo
                    FROM lecturas
                WHERE idparticipante = '".$idparticipante."'
                    AND (estado = 'ok' OR estado = 'repetido')
                    AND idcontrol = '".$mobile['idcontrol']."'"))) {
                $mobile['idcontrol'] = $temp_cambio_control[$mobile['idcontrol']];
            }
        }
        // PARCHE MISMO CONTROL ESPECÍFICO


        consulta_sql(
            "INSERT INTO lecturas SET
                uuid = uuid(),
                idcontrol = '".$mobile['idcontrol']."',
                idevento = '".$mobile['idevento']."',
                idparticipante = '".$idparticipante."',
                tiempo = '".$tiempo."',
                vuelta = ".$cant_vueltas.",
                timer = '".$cronometrador."',
                tipo = '".($tagID ? 'rfid' : 'app')."',
                estado = '".($cant_repetido ? 'repetido' : 'ok')."'
            ");

    }

    $resultado['idlectura'] = id_sql();
    $resultado['tiempo'] = $tiempo;
    $resultado['hora'] = $mostrar[1];
    $resultado['tiempo_listo'] = $tiempo_listo;
    $resultado['idparticipante'] = $idparticipante;
    $resultado['nombre'] = $participante['nombre']
        .($participante['apellido'] ? ' '.$participante['apellido'] : '');

    if ($resultado['idlectura'])
        cache($mobile['evento_codigo']);

    if (!$idparticipante || !$participante['nombre']) {
        $resultado['resultado'] = 'NO existe';
        log_api('Lectura; NO existe el participante '.$idparticipante.' ('.$tiempo_listo.' - '.$resultado['nombre'].' - tagID '.$tagID.')');
    } elseif ($idparticipante && $cant_repetido) { // No hace falta $idparticipante
        $resultado['resultado'] = 'NO repetido '.$cant_repetido.' veces';
        log_api('Lectura; NO repetido el participante '.$idparticipante.' ('.$tiempo_listo.' - '.$resultado['nombre'].' - tagID '.$tagID.')');
    } elseif ($mobile['tipo'] == 'acreditar') {
        $resultado['resultado'] = 'Acreditado en '.$participante['categoria'];
        log_api('Lectura; Acreditado '.$idparticipante.' ('.$tiempo_listo.' - '.$resultado['nombre'].' - tagID '.$tagID.')');
    } else {
        $resultado['resultado'] = 'OK'.($cant_vueltas > 1 ? ' Vuelta '.$cant_vueltas : '');
        log_api('Lectura; OK '.$idparticipante.' ('.$tiempo_listo.' - '.$resultado['nombre'].' - tagID '.$tagID.')');
    }

    if (!$idparticipante)
        $resultado['idparticipante'] = 'FALTA';

    if (in_array($mobile['idevento'], []) // Para habilitar eventos que puedan usar lo de la app
        && isset($participante['idinscripcion']) && $participante['idinscripcion']
        && contar_sql(consulta_sql(
        "SELECT iddato
        FROM `datosxeventos`
        WHERE idevento = '{$mobile['idevento']}'
            AND apps = 1
        LIMIT 1"))) {
        $resultado['extra'] = campo_sql(consulta_sql(
            "SELECT dato
            FROM datosxparticipantes
            WHERE idinscripcion = '{$participante['idinscripcion']}'
                AND iddato = (SELECT iddato FROM datosxeventos
                    WHERE apps = 1 AND idevento = '{$mobile['idevento']}' LIMIT 1)"
        ), 0, 'dato');
    } else {
        $resultado['extra'] = '';
    }

    if ($idparticipante && in_array($mobile['tipo'], ['largada', 'final', 'etapa']) && $mobile['streaming']) {

        $url = URL_API.'lecturas';
        $ch = curl_init( $url );
        # Setup request to send json via POST.
        $payload = json_encode([
            'idlectura' => $resultado['idlectura'],
        ]);
        curl_setopt( $ch, CURLOPT_POSTFIELDS, $payload );
        curl_setopt( $ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json'));
        # Return response instead of printing.
        curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
        # Send request.
        $result = curl_exec($ch);
        curl_close($ch);
        $resultado['refactor'] = $result;

    }
}
echo json_encode($resultado);
