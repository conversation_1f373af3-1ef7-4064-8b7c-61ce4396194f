<?php

exit('No lo estamos usando');

require __DIR__.'/../cronometrajeinstantaneo.env';
require __DIR__.'/../funciones.php';

$tiempo = filter_input(INPUT_POST, 'tiempo', FILTER_SANITIZE_SPECIAL_CHARS);
$codigo = filter_input(INPUT_POST, 'codigo', FILTER_SANITIZE_SPECIAL_CHARS);

$bd_link = conectar_db(BD_BD, BD_HOST, BD_USER, BD_PASS);
$mobile = session_mobile($codigo);

if (!$tiempo) {
    header("HTTP/1.1 400 Bad Request");
    exit('ERROR en tiempo');
}

consulta_sql("UPDATE eventos SET largada = '$tiempo' WHERE idevento = '".$mobile['idevento']."' LIMIT 1");
if (afectado_sql())
    cache($mobile['evento_codigo']);

log_api('Inicio; OK');
