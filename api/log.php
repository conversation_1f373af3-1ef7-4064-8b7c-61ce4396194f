<?php
require __DIR__.'/../cronometrajeinstantaneo.env';

header('Content-type: application/json');
header('Access-Control-Allow-Origin: *');

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    $time = filter_input(INPUT_GET, 'time');

    // Guardo en un log
    file_put_contents(PATH_LOGS.'/api-log_' . date("Y-m-d") . '-' . $time . '.log', file_get_contents('php://input').PHP_EOL, FILE_APPEND);

    echo file_get_contents('php://input');
    // Respondo con ok y termino
    header("HTTP/1.1 200 OK");
    exit();
}
