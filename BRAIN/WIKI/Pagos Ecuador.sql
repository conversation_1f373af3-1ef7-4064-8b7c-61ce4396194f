# Ver todos los pendientes

SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (151)) AND estado_pago = 'pendiente' AND eventos.estado != 'cancelado' ORDER BY fecha LIMIT 12;

# Poner como pagado en Crono y avisar

UPDATE eventos SET estado_pago = 'aprobado' WHERE idevento IN (2503);

# Enlaces

https://beta.cronometrajeinstantaneo.com/inicio/eventos/2503

# Por ahora cargué demás, solo hay que restar en la caja

https://scripts.saasargentina.com/?script=1&iue=MTVvtWMaVPdEGDpcsyU3&api_secret=i8eOimYHPv9bjPzF8de7q2OaSG2d0UYq&a=nuevo_pago_completo&idcliente=75&nombre=Ecuador&<EMAIL>&producto=representante&precio=50&descuento=0&total=50&moneda=2&pago=50&formapago=payoneer&observacion=&fecha=2025-04-12%2000:00:00&idservicio=2590
