function alerta(mensaje)
{
    alert(mensaje);
}

function confirma(mensaje)
{
    return confirm(mensaje);
}

function texto_tipolargada() {
    switch($("select[name=tipolargada] option:selected").val()) {
        default:
        case 'unica_largada':
            var texto_tipolargada = "En <b>Única largada punto a punto</b> todas las carreras, distancias, categorías y disciplinas del evento largan juntas en un mismo momento. Pueden llegar en distintos lugares pero tienen una sola etapa.";
            break;
        case 'unica_largada_con_etapas':
            var texto_tipolargada = "En <b>Única largada con varias etapas</b> todas las carreras, distancias, categorías y disciplinas del evento largan juntas en un mismo momento pero tienen distintas etapas. Por ejemplo: duatlón, triatlón, tetratlón.";
            break;
        case 'vueltas_fijas':
            var texto_tipolargada = 'En <b>Única largada con vueltas</b> todas las carreras, distancias, categorías y disciplinas del evento largan juntas en un mismo momento pero tienen que dar vueltas.';
            break;

        case 'carreras_con_largadas':
            var texto_tipolargada = "En <b>Carreras con largadas diferentes</b> las carreras tienen distintos horarios de largada. Pueden llegar juntas o en distintos lugares pero tienen una sola etapa.";
            break;
        case 'carreras_con_largadas_vueltas':
            var texto_tipolargada = "En <b>Carreras con largadas con vueltas</b> las carreras tienen distintos horarios de largada y tienen vueltas.";
            break;
        case 'carreras_con_largadas_con_etapas':
            var texto_tipolargada = "En <b>Carreras con largadas con etapas</b> las carreras tienen distintos horarios de largada y tienen etapas.";
            break;

        case 'etapas_con_largadas':
            var texto_tipolargada = "En <b>Etapas con largadas diferentes</b> las etapas tienen distintos horarios de largada. Pueden llegar juntas o en distintos lugares.";
            break;
        case 'etapas_con_largadas_vueltas':
            var texto_tipolargada = "En <b>Etapas con largadas diferentes y vueltas</b> las etapas tienen distintos horarios de largada. Pueden llegar juntas o en distintos lugares. Y algunas o todas las etapas tienen vueltas.";
            break;
        case 'etapas_suma_largadas_individuales':
            var texto_tipolargada = "En <b>Etapas con suma de largadas individuales</b> cada participante tiene su propio horario de largada y llegada en cada etapa. El tiempo final es la sumatoria del tiempo de todas las etapas.";
            break;

        case 'largadas_individuales':
            var texto_tipolargada = "En <b>Largadas individuales</b> cada participante tiene su propio horario de largada y llegada en una sola etapa.";
            break;
            case 'largadas_individuales_con_etapas':
                var texto_tipolargada = 'En <b>Largadas individuales con etapas</b> cada participante tiene su propio horario de largada y llegada en cada etapa (más de una).';
                break;
            case 'largadas_individuales_con_etapas_continuas':
                var texto_tipolargada = 'En <b>Largadas individuales con etapas continuas</b> cada participante tiene su propio horario de largada pero debe pasar por todas las etapas sin parar.';
                break;
            case 'largadas_individuales_con_vueltas':
            var texto_tipolargada = 'En <b>Largadas individuales con vueltas</b> cada participante tiene su propio horario de largada y llegada y por lo menos una etapa tiene más de una vuelta.';
            break;
    }
    $("#texto_tipolargada").html(texto_tipolargada);
}

function validar_tiempo(input)
{
    var tempInput = input.split("");
    if (tempInput.length != 23) return false;
    if (tempInput[0] !== '2') return false;
    if (tempInput[1] !== '0') return false;
    if (isNaN(tempInput[2])) return false;
    if (isNaN(tempInput[3])) return false;
    if (tempInput[4] !== '-') return false;
    if (isNaN(tempInput[5])) return false;
    if (isNaN(tempInput[6])) return false;
    if (tempInput[7] !== '-') return false;
    if (isNaN(tempInput[8])) return false;
    if (isNaN(tempInput[9])) return false;
    if (tempInput[10] !== ' ') return false;
    if (isNaN(tempInput[11])) return false;
    if (isNaN(tempInput[12])) return false;
    if (tempInput[13] !== ':') return false;
    if (isNaN(tempInput[14])) return false;
    if (isNaN(tempInput[15])) return false;
    if (tempInput[16] !== ':') return false;
    if (isNaN(tempInput[17])) return false;
    if (isNaN(tempInput[18])) return false;
    if (tempInput[19] !== '.') return false;
    if (isNaN(tempInput[20])) return false;
    if (isNaN(tempInput[21])) return false;
    if (isNaN(tempInput[22])) return false;

    if (parseInt(tempInput[5]) > 1) return false;
    if (parseInt(tempInput[5]) == 1 && parseInt(tempInput[6]) > 2) return false;

    if (parseInt(tempInput[8]) > 3) return false;
    if (parseInt(tempInput[8]) == 3 && parseInt(tempInput[9]) > 1) return false;

    if (parseInt(tempInput[11]) > 2) return false;
    if (parseInt(tempInput[11]) == 2 && parseInt(tempInput[12]) > 4) return false;

    if (parseInt(tempInput[14]) > 5) return false;
    if (parseInt(tempInput[17]) > 5) return false;

    return true;
}

function validar_tiempo_listo(input)
{
    var tempInput = input.split("");
    if (tempInput.length > 16 || tempInput.length < 11) return false;
    if (isNaN(tempInput[tempInput.length-11])) return false;
    if (tempInput[tempInput.length-10] !== ':') return false;
    if (isNaN(tempInput[tempInput.length-9])) return false;
    if (isNaN(tempInput[tempInput.length-8])) return false;
    if (tempInput[tempInput.length-7] !== ':') return false;
    if (isNaN(tempInput[tempInput.length-6])) return false;
    if (isNaN(tempInput[tempInput.length-5])) return false;
    if (tempInput[tempInput.length-4] !== '.') return false;
    if (isNaN(tempInput[tempInput.length-3])) return false;
    if (isNaN(tempInput[tempInput.length-2])) return false;
    if (isNaN(tempInput[tempInput.length-1])) return false;

    if (parseInt(tempInput[tempInput.length-9]) > 5) return false;
    if (parseInt(tempInput[tempInput.length-6]) > 5) return false;

    for (i = 0; i < (tempInput.length - 11); i++) {
        if (isNaN(tempInput[i])) return false;
    }

    return true;
}

function validar_inputs_lecturas(parent)
{
    var inputTiempo = parent.find("input[name='tiempo']");
    var inputTiempoListo = parent.find("input[name='tiempo_listo']");

    if (!validar_tiempo(inputTiempo.val())) {
        alert("La hora es incorrecta");
        return false;
    }

    if (!validar_tiempo_listo(inputTiempoListo.val())) {
        alert("El tiempo es incorrecto");
        return false;
    }

    return true;

}

function tiempoToUnix(tiempo)
{
    var temp = tiempo.split(" ");

    if (temp.length == 1) {
        var tempMs = temp[0].split(".");
        var tempHora = tempMs[0].split(":");

        var unix = parseFloat(tempMs[1])
            + parseFloat(tempHora[2] * 1000) // Segundos
            + parseFloat(tempHora[1] * 60 * 1000) // Minutos
            + parseFloat(tempHora[0] * 3600 * 1000); // Horas

    } else {
        var tempDia = temp[0].split("-");
        var tempMs = temp[1].split(".");
        var tempHora = tempMs[0].split(":");

        var date = new Date(tempDia[0], (tempDia[1] - 1), tempDia[2], tempHora[0], tempHora[1], tempHora[2], tempMs[1]);
        var unix = Math.round(date.getTime());
    }

    return unix;
}

function unixToTiempo(unix)
{
    var date = new Date(unix);

    var dia = date.getFullYear() + "-"
        + ((date.getMonth() < 9) ? "0" : "") + (date.getMonth()+1) + "-"
        + ((date.getDate() < 10) ? "0" : "") + date.getDate();
    var hora = ((date.getHours() < 10) ? "0" : "") + date.getHours() + ":"
        + ((date.getMinutes() < 10) ? "0" : "") + date.getMinutes() + ":"
        + ((date.getSeconds() < 10) ? "0" : "") + date.getSeconds();
    var milisegundos =
        (date.getMilliseconds() == 0 ? "" : "") +
        (date.getMilliseconds() < 10 ? "0" : "") +
        (date.getMilliseconds() < 100 ? "0" : "") +
        date.getMilliseconds();

    return tiempo = dia + " " + hora + "." + milisegundos;
}

function unixToTiempoListo(unix)
{
    var tempUnix = unix;
    var milisegundos = Math.round((tempUnix / 1000 - Math.trunc(tempUnix / 1000)) * 1000);
    tempUnix = Math.trunc(tempUnix / 1000);
    var segundos = Math.round((tempUnix / 60 - Math.trunc(tempUnix / 60)) * 60);
    tempUnix = Math.trunc(tempUnix / 60);
    var minutos = Math.round((tempUnix / 60 - Math.trunc(tempUnix / 60)) * 60);
    var horas = Math.trunc(tempUnix / 60);

    var tiempo = ((horas < 10) ? "0" : "") + horas + ":"
        + ((minutos < 10) ? "0" : "") + minutos + ":"
        + ((segundos < 10) ? "0" : "") + segundos + "."
        + ((milisegundos < 100) ? "0" : "") + ((milisegundos < 10) ? "0" : "") + milisegundos;

    return tiempo;
}


function change_tiempo(element, tipo)
{
    var parent = element.parent().parent();
    var inputTiempo = parent.find("input[name='tiempo']");
    var inputTiempoOriginal = parent.find("input[name='tiempo_original']");
    var inputTiempoListo = parent.find("input[name='tiempo_listo']");
    var inputTiempoListoOriginal = parent.find("input[name='tiempo_listo_original']");

    if (!validar_inputs_lecturas(parent)) {
        if (tipo == 'tiempo')
            inputTiempo.val(inputTiempoOriginal.val());
        else
            inputTiempoListo.val(inputTiempoListoOriginal.val());
        return false;
    }

    switch (tipo) {
        case "tiempo":
            var unixTiempo = tiempoToUnix(inputTiempo.val());
            var unixTiempoOriginal = tiempoToUnix(inputTiempoOriginal.val());

            var diferencia = unixTiempo - unixTiempoOriginal;
            if (tiempoToUnix(inputTiempoListo.val()) + diferencia  < 0) {
                alert('La hora no puede ser anterior al inicio del evento');
                inputTiempo.val(inputTiempoOriginal.val());
                return false;
            }

            var nuevoTiempoListo = unixToTiempoListo( tiempoToUnix(inputTiempoListo.val()) + diferencia );

            inputTiempoListo.val(nuevoTiempoListo);
            inputTiempoListoOriginal.val(nuevoTiempoListo);

            inputTiempoOriginal.val(inputTiempo.val());
            break;

        case "tiempo_listo":
            var unixTiempoListo = tiempoToUnix(inputTiempoListo.val());
            var unixTiempoListoOriginal = tiempoToUnix(inputTiempoListoOriginal.val());

            var diferencia = unixTiempoListo - unixTiempoListoOriginal;
            var nuevoTiempo = unixToTiempo( tiempoToUnix(inputTiempo.val()) + diferencia );

            inputTiempo.val(nuevoTiempo);
            inputTiempoOriginal.val(nuevoTiempo);

            inputTiempoListoOriginal.val(inputTiempoListo.val());
            break;

        case "mas_seg":
        case "menos_seg":
            var diferencia = tipo == "mas_seg" ? 1000 : -1000;

            var nuevoTiempo = unixToTiempo( tiempoToUnix(inputTiempo.val()) + diferencia );
            var nuevoTiempoListo = unixToTiempoListo( tiempoToUnix(inputTiempoListo.val()) + diferencia );

            inputTiempo.val(nuevoTiempo);
            inputTiempoOriginal.val(nuevoTiempo);

            inputTiempoListo.val(nuevoTiempoListo);
            inputTiempoListoOriginal.val(nuevoTiempoListo);
            break;
    }
}

function ok_lectura(element)
{
    var parent = element.parent().parent();
    if (!validar_inputs_lecturas(parent))
        return false;

    if (confirma("¿Está seguro que desea modificar?")) {

        var codigo = parent.find("input[name='codigo']").val();
        var idlectura = parent.find("input[name='idlectura']").val();
        var idparticipante = parent.find("input[name='idparticipante']").val();
        var tiempo = parent.find("input[name='tiempo']").val();
        var estado = parent.find("select[name='estado']").val();
        var _token = $("form input[name=_token]").val();

        var datos = {
            codigo: codigo,
            idlectura: idlectura,
            idparticipante: idparticipante,
            tiempo: tiempo,
            estado: estado,
            _token: _token
        };

        $.ajax({
            url: URL_API_OLD + "mod.php",
            type:"POST",
            dataType: "json",
            data: datos,
            beforeSend: function () {
                parent.addClass('enviando');
            },
            success: function (data) {
                if (data.resultado == 'EVENTO TERMINADO')
                    alert("No se modificó el tiempo porque el evento se encuentra terminado");
                else
                    parent.removeClass('enviando');
            },
            error: function (data) {
                alert("Error de conexión al modificar");
            }
        });
    }
}

function baja_lectura(element)
{
    var parent = element.parent().parent();
    if (!validar_inputs_lecturas(parent))
        return false;

    if (confirma("¿Está seguro que desea borrar?")) {
        var codigo = parent.find("input[name='codigo']").val();
        var idlectura = parent.find("input[name='idlectura']").val();
        var _token = $("form input[name=_token]").val();

        var datos = {
            codigo: codigo,
            idlectura: idlectura,
            _token: _token
        };

        $.ajax(
        {
            url: URL_API_OLD + "baja.php",
            type:"POST",
            data: datos,
            beforeSend: function () {
                parent.addClass('enviando');
            },
            success: function(data) {
                if (data.resultado == 'EVENTO TERMINADO')
                    alert("No se modificó el tiempo porque el evento se encuentra terminado");
                else
                    parent.remove();
            },
            error: function (data) {
                alert("Error de conexión al borrar");
            }
        });
    }
}

function alta_lectura(element)
{
    var parent = element.parent().parent();
    if (!validar_inputs_lecturas(parent))
        return false;

    if (confirma("¿Está seguro que desea agregar este tiempo?")) {

        var codigo = parent.find("select[name='codigo']").val();
        var idparticipante = parent.find("input[name='idparticipante']").val();
        var tiempo = parent.find("input[name='tiempo']").val();
        var _token = $("form input[name=_token]").val();

        var datos = {
            codigo: codigo,
            idparticipante: idparticipante,
            cronometrador: 'Admin',
            tiempo: tiempo,
            _token: _token
        };

        $.ajax({
            url: URL_API_OLD + "lectura.php",
            type:"POST",
            dataType: "json",
            data: datos,
            beforeSend: function () {
                parent.addClass('enviando');
            },
            success: function (data) {
                if (data.resultado == 'EVENTO TERMINADO')
                    alert("No se agregó el tiempo porque el evento se encuentra terminado");
                else
                    location.reload();
            },
            error: function (data) {
                alert("Error de conexión al modificar");
            }
        });
    }
}

function submit_largadas(element)
{
    var parent = element.parent();
    var accion = element.attr("name");
    var id = parent.find("input[name=id]").val();
    var largada = parent.find("input[name='"+accion+"_"+id+"']").val();

    if (!largada && element.val() != "MODIFICAR") {
        var date = new Date();
        var milisegundos =
            (date.getMilliseconds() == 0 ? "" : "") +
            (date.getMilliseconds() < 10 ? "0" : "") +
            (date.getMilliseconds() < 100 ? "0" : "") +
            date.getMilliseconds();

        largada =
            + date.getFullYear() + "-"
            + ("0" + (date.getMonth() + 1)).slice(-2) + "-"
            + ("0" + date.getDate()).slice(-2) + " "
            + ((date.getHours() < 10) ? "0" : "") + date.getHours() + ":"
            + ((date.getMinutes() < 10) ? "0" : "") + date.getMinutes() + ":"
            + ((date.getSeconds() < 10) ? "0" : "") + date.getSeconds() + "."
            + milisegundos;

        parent.find("input[name='"+accion+"_"+id+"']").val(largada);
    }

    $.ajax({
        url: "ajax",
        type: "post",
        data: ({
            accion: accion,
            id: id,
            largada: largada,
            _token: $("input[name=_token]").val()
        }),
        beforeSend: function() {
            if (accion == "reset")
                return confirma("¡¡ CUIDADO !!\n\r Este proceso elimina todos los datos del cronometraje del evento y NO se pueden recuperar.\n\r¿Está seguro que desea continuar?");
        },
        success: function(data) {
            if (accion == "reset")
                history.go(0);
            else if (largada)
                element.val("MODIFICAR");
            else if (accion == "terminada_evento")
                element.val("FINALIZAR");
            else
                element.val("INICIAR");
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status + " - " + textStatus + " - " + unescape(errorThrown));
            alert("Ocurrió un error con la conexión, por favor intente más tarde");
        }
    });
}

function click_listado(element, accion)
{
    var tr = element.parent().parent();
    var id = element.attr("id");
    var img = element.children();
    var ajax = '<img class="ajax" src="estilos/img/ajax.gif" title="Conectando con el servidor...">';
    switch (accion) {
        case "baja": var mensaje = "¿Está seguro que desea ANULAR a esta inscripción?"; break;
        case "ficha": var mensaje = "¿Está seguro que desea APROBAR los DATOS de esta inscripción?"; break;
        case "pago": var mensaje = "¿Está seguro que desea APROBAR el PAGO de esta inscripción?"; break;
        case "acreditar" : var mensaje = "¿Está seguro que desea ACREDITAR a este participante?"; break;
    }
    if (confirma(mensaje)) {
        request = $.ajax({
            url: "ajax",
            type: "post",
            data: ({
                accion: accion,
                id: id,
                _token: $("input[name=_token]").val()
            }),
            beforeSend: function() {
                img.hide();
                element.append(ajax);
            },
            success: function(data) {
                tr.replaceWith(data);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                element.children("img.ajax").remove();
                img.show();
                console.log(jqXHR.status + " - " + textStatus + " - " + unescape(errorThrown));
                alert("Ocurrió un error con la conexión, por favor intente más tarde");
            }
        });
    }
    return false;
}

function eliminar_anulados()
{
    if (confirma("¿Está seguro que desea ELIMINAR a los participantes anulados?")) {
        request = $.ajax({
            url: "ajax",
            type: "post",
            data: ({
                accion: "eliminar_anulados",
                _token: $("input[name=_token]").val()
            }),
            success: function(data) {
                window.history.go(0);
            }
        });
    }
}

function alta_config(element)
{
    var parent = element.parent().parent();
    var recurso = parent.find("input[name=tabla]").val();
    var _token = $("form input[name=_token]").val();

    if (recurso == 'precios') {
        var categorias = $("select[name=categorias]").val();
        var formadepago = $("select[name=formadepago]").val();
        var data = parent.find("input").serialize();
        var datos = {
            categorias: categorias,
            formadepago: formadepago,
            datos: data,
            _token: _token
        }
    } else {
        var datos = parent.find("input, select").serializeArray();
        datos.push({name: '_token', value: _token});
    }

    $.ajax({
        url: URL_API + recurso,
        type:"POST",
        dataType: "json",
        data: datos,
        beforeSend: function () {
            parent.addClass('enviando');
        },
        success: function (data) {
            location.reload();
        },
        error: function (data) {
            ajax_error(data);
        }
    });
    return false;
}

function ok_config(element)
{
    var parent = element.parent().parent();
    var recurso = parent.find("input[name=tabla]").val();
    var id = $.trim(parent.find("input[name=id]").val());
    var _token = $("form input[name=_token]").val();

    if (recurso == 'precios') {
        var categorias = $("select[name=categorias_mod_"+$.trim(id)+"]").val();
        var formadepago = $("select[name=formadepago_mod_"+$.trim(id)+"]").val();
        var data = parent.find("input").serialize();

        var datos = {
            categorias: categorias,
            formadepago: formadepago,
            datos: data,
            _token: _token
        }
    } else {
        var datos = parent.find("input, select").serializeArray();
        datos.push({name: '_token', value: _token});
    }

    $.ajax({
        url: URL_API + recurso + "/" + id,
        type:"PUT",
        dataType: "json",
        data: datos,
        beforeSend: function () {
            parent.addClass('enviando');
        },
        success: function (data) {
            parent.removeClass('enviando');
            if (recurso == 'precios') location.reload();
        },
        error: function (data) {
            ajax_error(data);
        }
    });
    return false;
}

function baja_config(element)
{
    var parent = element.parent().parent();
    if (confirma("¿Está seguro que desea borrar?")) {

        var recurso = parent.find("input[name=tabla]").val();
        var id = parent.find("input[name=id]").val();
        var _token = $("form input[name=_token]").val();

        if (recurso == 'controles') {
            idetapa = parent.find("input[name=idetapa]").val();
            id = id + '-' + idetapa;
        }


        $.ajax(
        {
            url: URL_API + recurso + "/" + id,
            type:"DELETE",
            data: {
                _token: _token
            },
            beforeSend: function () {
                parent.addClass('enviando');
            },
            success: function(data) {
                parent.remove();
            },
            error: function (data) {
                ajax_error(data);
            }
        });
    }
    return false;
}

function select_datos_extras()
{
    var datos = JSON.parse(window.datos);
    var select = $("select[name=iddato]");
    select.empty();
    for (var dato in datos) {
        if (datos.hasOwnProperty(dato)) {
        select.append('<option value="'+dato+'">'+datos[dato]+'</option>');
        }
    }
}

function ajax_error(data)
{
    if (!data.responseText.length) {
        alerta('Error conectando al servidor');
        return false
    }

    var responseText = JSON.parse(data.responseText);
    var mensaje = '';
    Object.keys(responseText.errors).forEach(function(key) {
        mensaje += responseText.errors[key] + '\n';
    });
    alerta(mensaje);
}

// Duplicado del módulo de inscripciones
function populate_cat(categoria, idcat)
{
    $('#categorias').append($('<option>', {
        value: categoria.idcategoria,
        text: categoria.nombre,
        idcat: idcat
    }));
}

// Esta funcion se utiliza para la multicategoría, agregando todas las categorías de todas las carreras cuando se selecciona el sexo. No tiene en cuenta el nacimiento.
function allCategoria()
{
    var idcategorias = $("#idcategorias").val();
    console.log(idcategorias);
    $('#categorias').empty();
    if ($("input[name='sexo']").length == 1)
        $("input[name='sexo']").prop("checked", "checked");
    if ($("input[name='idcarrera']").length == 1)
        $("input[name='idcarrera']").prop("checked", "checked");

    var sexo = $("input[name='sexo']:checked").val();
    if (!sexo) sexo = $( "#sexo" ).val(); // chequeo si no viene del select de parti manual
    // var idcarrera = $("input[name='idcarrera']:checked").val();
    // if (!idcarrera) idcarrera = $( "#idcarrera" ).val(); // chequeo si no viene del select de parti manual
    var idcat = 0;
    $.each(categorias, function (i, categoria) {
        if ((sexo == 'masculino' && (categoria.sexo == 'masculino' || categoria.sexo == 'mixto'))
            || (sexo == 'femenino' && (categoria.sexo == 'femenino' || categoria.sexo == 'mixto'))
            || (sexo == 'otro' && (categoria.sexo == 'otro' || categoria.sexo == 'mixto'))
            || (sexo == 'equipomasculino' && categoria.sexo == 'equipomasculino')
            || (sexo == 'equipofemenino' && categoria.sexo == 'equipofemenino')
            || (sexo == 'equipootro' && categoria.sexo == 'equipootro')
            || (sexo == 'equipomixto' && categoria.sexo == 'equipomixto')
            ) {
            idcat++;
            populate_cat(categoria, idcat);
        }
    });
    var idcategoriasArray = idcategorias.split(',');
    $('#categorias').val(idcategoriasArray)
    // el primer valor es el id de la carrera, lo asigno a #idcategoria
    $('#idcategoria').val(idcategoriasArray[0]);

}

function selectCategoria()
{
    var idcategoria = $("#idcategoria").val();
    $('#categorias').empty();
    $('#categorias').append($('<option>', {
        value: "",
        text: "Seleccione el sexo y participación para ver las categorías correspondientes"
    }));
    if ($("input[name='sexo']").length == 1)
        $("input[name='sexo']").prop("checked", "checked");
    if ($("input[name='idcarrera']").length == 1)
        $("input[name='idcarrera']").prop("checked", "checked");

    var sexo = $("input[name='sexo']:checked").val();
    if (!sexo) sexo = $( "#sexo" ).val(); // chequeo si no viene del select de parti manual
    var idcarrera = $("input[name='idcarrera']:checked").val();
    if (!idcarrera) idcarrera = $( "#idcarrera" ).val(); // chequeo si no viene del select de parti manual
    var nacimiento_datoxevento = $('#nacimiento_datoxevento').val();

    if (sexo != undefined && idcarrera != undefined) {
        var idcat = 0;
        $.each(categorias, function (i, categoria) {
            if (idcarrera == categoria.idcarrera
                && ((sexo == 'masculino' && (categoria.sexo == 'masculino' || categoria.sexo == 'mixto'))
                || (sexo == 'femenino' && (categoria.sexo == 'femenino' || categoria.sexo == 'mixto'))
                || (sexo == 'otro' && categoria.sexo == 'otro')
                || (sexo == 'equipomasculino' && categoria.sexo == 'equipomasculino')
                || (sexo == 'equipofemenino' && categoria.sexo == 'equipofemenino')
                || (sexo == 'equipootro' && categoria.sexo == 'equipootro')
                || (sexo == 'equipomixto' && categoria.sexo == 'equipomixto')
                )) {

                if ((typeof nacimiento_datoxevento == 'undefined' || !nacimiento_datoxevento)
                    || categoria.nacimiento_desde == null
                    || categoria.nacimiento_desde == '0000-00-00'
                    || categoria.nacimiento_hasta == null
                    || categoria.nacimiento_hasta == '0000-00-00'
                    ) {
                    idcat++;
                    populate_cat(categoria, idcat);

                } else {
                    var temp_nac_dxe = new Date(nacimiento_datoxevento);
                    var temp_nac_desde = new Date(categoria.nacimiento_desde);
                    var temp_nac_hasta = new Date(categoria.nacimiento_hasta);
                    if (temp_nac_desde <= temp_nac_dxe && temp_nac_hasta >= temp_nac_dxe){
                        idcat++;
                        populate_cat(categoria, idcat);
                    }
                }

            }
        });
        $("#categorias").val($('#categorias').find('option[value="'+idcategoria+'"]').val());
        if (idcat == 1) {
            $("#categorias").val($('#categorias').find('option[idcat="1"]').val());
        } else if (idcat == 0) {
            $('#categorias').empty().append($('<option>', {
                value: "",
                text: "No hay categorías disponibles para el género y participación seleccionados"
            }));
        } else {
            ordenar_categorias();
            $("#categorias").val("");
        }
    }
}

function copyClipboard(textToCopy) {
    navigator.clipboard.writeText(textToCopy);
}

function agregarParametrosMenu(element, parametros)
{
    let href_original = element.parentElement.parentElement.getAttribute('href');
    let href = href_original;
    parametros.forEach(function(parametro) {
        href += (href.indexOf('?') == -1 ? '?' : '&') + parametro;
    });
    element.parentElement.parentElement.setAttribute('href', href);
    // Forzar clic sobre parentElement
    element.parentElement.click();
    element.parentElement.parentElement.setAttribute('href', href_original);
    return false;
}
