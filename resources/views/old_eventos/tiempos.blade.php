<?php
$bd_link = FuncionesComunes::conectar_db();

// Función para parsear el filtro de participantes
function parseParticipantFilter($filter) {
    if (empty($filter)) return [];

    $participants = [];
    $parts = explode(',', $filter);

    foreach ($parts as $part) {
        $part = trim($part);
        if (strpos($part, '-') !== false) {
            // Es un rango
            list($start, $end) = explode('-', $part, 2);
            $start = intval(trim($start));
            $end = intval(trim($end));
            if ($start <= $end) {
                for ($i = $start; $i <= $end; $i++) {
                    $participants[] = $i;
                }
            }
        } else {
            // Es un número individual
            $num = intval($part);
            if ($num > 0) {
                $participants[] = $num;
            }
        }
    }

    return array_unique($participants);
}

// Función para validar datetime
function validateDateTime($datetime) {
    if (empty($datetime)) return true;

    // Intentar varios formatos comunes
    $formats = ['Y-m-d H:i:s', 'Y-m-d H:i', 'Y-m-d'];

    foreach ($formats as $format) {
        $d = DateTime::createFromFormat($format, $datetime);
        if ($d && $d->format($format) === $datetime) {
            return true;
        }
    }

    return false;
}

// Procesar filtros
$filtros_aplicados = false;

// Si se presionó limpiar filtros, resetear todo
if (isset($_POST['limpiar_filtros'])) {
    $participantes_filter = '';
    $controles_filter = [];
    $hora_desde = '';
    $hora_hasta = '';
    $cronometrador_filter = '';
    $estado_filter = ['ok'];
    $filtros_aplicados = false;
} else {
    $participantes_filter = isset($_POST['participantes_filter']) ? trim($_POST['participantes_filter']) : '';
    $controles_filter = isset($_POST['controles_filter']) ? $_POST['controles_filter'] : [];
    $hora_desde = isset($_POST['hora_desde']) ? trim($_POST['hora_desde']) : '';
    $hora_hasta = isset($_POST['hora_hasta']) ? trim($_POST['hora_hasta']) : '';
    $cronometrador_filter = isset($_POST['cronometrador_filter']) ? trim($_POST['cronometrador_filter']) : '';

    // Estado por defecto es 'ok' si no se ha enviado el formulario, sino usar lo enviado
    if (isset($_POST['estado_filter'])) {
        $estado_filter = $_POST['estado_filter'];
    } else {
        $estado_filter = ['ok']; // Por defecto solo mostrar estados 'ok'
    }
}

// Validar fechas
$error_fecha = '';
if (!empty($hora_desde) && !validateDateTime($hora_desde)) {
    $error_fecha = 'Formato de "Hora desde" inválido. Use: YYYY-MM-DD HH:MM:SS';
}
if (!empty($hora_hasta) && !validateDateTime($hora_hasta)) {
    $error_fecha = 'Formato de "Hora hasta" inválido. Use: YYYY-MM-DD HH:MM:SS';
}

// Determinar si hay filtros aplicados
if (!empty($participantes_filter) || !empty($controles_filter) || !empty($hora_desde) ||
    !empty($hora_hasta) || !empty($cronometrador_filter) ||
    (isset($_POST['estado_filter']) && (in_array('todos', $estado_filter) ||
     !array_diff($estado_filter, ['ok']) === false))) {
    $filtros_aplicados = true;
}

// Si se envió el formulario, siempre mostrar resultados
if ($_POST) {
    $filtros_aplicados = true;
}

$idparticipante = isset($_GET['idparticipante'])
    ? filter_input(INPUT_GET, 'idparticipante', FILTER_VALIDATE_INT)
    : filter_input(INPUT_POST, 'idparticipante', FILTER_VALIDATE_INT);

$controles_sql = FuncionesComunes::consulta_sql(
    "SELECT idcontrol, nombre, codigo
    FROM controles
    WHERE idevento = '{$evento->idevento}'
        AND tipo IN ('pre-largada', 'pre-llegada', 'largada', 'control', 'parcial', 'etapa', 'final')
    GROUP BY idcontrol
    ORDER BY orden");

?>
@include('old_admin/head')
<meta name="csrf-token" content="{{ csrf_token() }}">
@include('old_admin/menu')

<h1>Modificar los tiempos de los controles</h1>

    <div>
        <form method="post" accept-charset="utf-8">
            @csrf
            <input type="number" name="idparticipante" value="<?= $idparticipante ?>" />
            <input type="submit" name="filtrar" value="Filtrar por N°" />
        </form>
    </div>

    <!-- Formulario de Filtros -->
    <div style="background-color: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px;">
        <h3>Filtros de Búsqueda</h3>

        <?php if (!empty($error_fecha)): ?>
            <div style="color: red; margin-bottom: 10px;"><?php echo $error_fecha; ?></div>
        <?php endif; ?>

        <form method="POST" action="">
            @csrf
            <table style="width: 100%;">
                <tr>
                    <td style="padding: 5px; width: 20%;">
                        <label for="participantes_filter"><strong>N° de Participante:</strong></label><br>
                        <input type="text" id="participantes_filter" name="participantes_filter"
                               value="<?php echo htmlspecialchars($participantes_filter); ?>"
                               placeholder="Ej: 10,15-20,25" style="width: 100%;">
                        <small>Números individuales, rangos (10-20) o listas (10,11,12)</small>
                    </td>

                    <td style="padding: 5px; width: 20%;">
                        <label for="controles_filter"><strong>Controles:</strong></label><br>
                        <select id="controles_filter" name="controles_filter[]" multiple style="width: 100%; height: 60px;">
                            <option value="">Todos los controles</option>
                            <?php
                            FuncionesComunes::puntero_sql($controles_sql, 0);
                            while ($control = FuncionesComunes::array_sql($controles_sql)): ?>
                                <option value="<?php echo $control['idcontrol']; ?>"
                                        <?php echo in_array($control['idcontrol'], $controles_filter) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($control['nombre'] . ' (' . $control['codigo'] . ')'); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </td>

                    <td style="padding: 5px; width: 15%;">
                        <label for="hora_desde"><strong>Hora desde:</strong></label><br>
                        <input type="text" id="hora_desde" name="hora_desde"
                               value="<?php echo htmlspecialchars($hora_desde); ?>"
                               placeholder="YYYY-MM-DD HH:MM:SS" style="width: 100%;">
                        <small>Formato: YYYY-MM-DD HH:MM:SS</small>
                    </td>

                    <td style="padding: 5px; width: 15%;">
                        <label for="hora_hasta"><strong>Hora hasta:</strong></label><br>
                        <input type="text" id="hora_hasta" name="hora_hasta"
                               value="<?php echo htmlspecialchars($hora_hasta); ?>"
                               placeholder="YYYY-MM-DD HH:MM:SS" style="width: 100%;">
                        <small>Formato: YYYY-MM-DD HH:MM:SS</small>
                    </td>

                    <td style="padding: 5px; width: 15%;">
                        <label for="cronometrador_filter"><strong>Cronometrador:</strong></label><br>
                        <input type="text" id="cronometrador_filter" name="cronometrador_filter"
                               value="<?php echo htmlspecialchars($cronometrador_filter); ?>"
                               placeholder="Buscar por cronometrador" style="width: 100%;">
                    </td>

                    <td style="padding: 5px; width: 15%;">
                        <label for="estado_filter"><strong>Estado:</strong></label><br>
                        <select id="estado_filter" name="estado_filter[]" multiple style="width: 100%; height: 60px;">
                            <option value="todos" <?php echo in_array('todos', $estado_filter) ? 'selected' : ''; ?>>Todos los estados</option>
                            <option value="ok" <?php echo in_array('ok', $estado_filter) ? 'selected' : ''; ?>>OK</option>
                            <option value="error" <?php echo in_array('error', $estado_filter) ? 'selected' : ''; ?>>Error</option>
                            <option value="eliminado" <?php echo in_array('eliminado', $estado_filter) ? 'selected' : ''; ?>>Eliminado</option>
                            <option value="repetido" <?php echo in_array('repetido', $estado_filter) ? 'selected' : ''; ?>>Repetido</option>
                            <option value="hooked" <?php echo in_array('hooked', $estado_filter) ? 'selected' : ''; ?>>Hooked</option>
                            <option value="lap" <?php echo in_array('lap', $estado_filter) ? 'selected' : ''; ?>>Lap</option>
                            <option value="dnf" <?php echo in_array('dnf', $estado_filter) ? 'selected' : ''; ?>>DNF</option>
                            <option value="dsq" <?php echo in_array('dsq', $estado_filter) ? 'selected' : ''; ?>>DSQ</option>
                            <option value="dns" <?php echo in_array('dns', $estado_filter) ? 'selected' : ''; ?>>DNS</option>
                            <option value="mss" <?php echo in_array('mss', $estado_filter) ? 'selected' : ''; ?>>MSS</option>
                            <option value="otro" <?php echo in_array('otro', $estado_filter) ? 'selected' : ''; ?>>Otro</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td colspan="7" style="padding: 10px; text-align: center;">
                        <button type="submit" style="padding: 8px 16px; margin-right: 10px;">Aplicar Filtros</button>
                        <button type="submit" name="limpiar_filtros" value="1"
                                style="padding: 8px 16px; background-color: #6c757d; color: white; border: none; border-radius: 3px;">Limpiar</button>
                    </td>
                </tr>
            </table>
        </form>
    </div>

    <?php if (!$filtros_aplicados && empty($error_fecha)): ?>
        <!-- Mensaje cuando no hay filtros aplicados -->
        <div style="background-color: #e9ecef; padding: 30px; margin: 20px 0; border-radius: 5px; text-align: center;">
            <h3>Seleccione los filtros correspondientes para mostrar las lecturas</h3>
            <p style="color: #6c757d;">Use el formulario de arriba para filtrar las lecturas por participante, control, hora, cronometrador o estado.</p>
        </div>
    <?php elseif ($filtros_aplicados && empty($error_fecha)): ?>

    <table>
        <tr>
            <th colspan="7" style="width: 9%; text-align: center;">Agregar un tiempo</th>
        </tr>
        <tr>
            <th style="text-align: center; width: 7%; min-width: 70px;">Nº</th>
            <th style="text-align: center; width: 15%;">Hora</th>
            <th style="text-align: center; width: 15%;">Tiempo</th>
            <th style="text-align: center; width: 15%;">Código</th>
            <th style="text-align: center;">Estado</th>
            <th style="text-align: center; width: 20%;">Acciones</th>
        </tr>
<?php
echo '
        <tr>
            <td style="text-align: center;"><input type="number" name="idparticipante" value="" /></td>
            <td style="text-align: center;"><input type="text" name="tiempo" value="'.$evento->largada.'" onchange="change_tiempo($(this), \'tiempo\')" /></td>
            <td style="text-align: center;"><input type="text" name="tiempo_listo" value="00:00:00.000" onchange="change_tiempo($(this), \'tiempo_listo\')" /></td>
            <td style="text-align: center;">
                <select name="codigo">';
    while ($control = FuncionesComunes::array_sql($controles_sql)) {
        echo '<option value="'.$control['codigo'].'">'.$control['nombre'].' ('.$control['codigo'].')</option>';
    }
    $tiempo_listo = FuncionesComunes::convertirFinal(0);
    echo '
                </select>
            </td>
            <td style="text-align: center;">
                <select>
                    <option value="ok">OK</option>
                    <option value="error">Error</option>
                    <option value="eliminado">Eliminado</option>
                    <option value="repetido">Repetido</option>
                    <option value="hooked">Hooked</option>
                    <option value="lap">Lap</option>
                    <option value="dnf">DNF</option>
                    <option value="dsq">DSQ</option>
                    <option value="dns">DNS</option>
                    <option value="mss">MSS</option>
                    <option value="otro">Otro</option>
                </select>
            </td>
            <td style="text-align: center;">
                <input type="hidden" name="tiempo_original" value="'. $evento->largada .'" />
                <input type="hidden" name="tiempo_listo_original" value="'. $tiempo_listo .'" />
                <button onclick="alta_lectura($(this))">Agregar</button>
            </td>
        </tr>';
FuncionesComunes::puntero_sql($controles_sql, 0);
while ($control = FuncionesComunes::array_sql($controles_sql)) {

    echo '
        <tr>
            <td colspan="7">&nbsp;</td>
        </tr>
        <tr>
            <th colspan="7" style="text-align: center;">'.$control['nombre'].' (Código: '.$control['codigo'].')</th>
        </tr>
        <tr>
            <th style="text-align: center;">Nº</th>
            <th style="text-align: center;">Hora</th>
            <th style="text-align: center;">Tiempo</th>
            <th style="text-align: center;">Cronometrador</th>
            <th style="text-align: center;">Estado</th>
            <th style="text-align: center;">Acciones</th>
        </tr>';

    // Construir condiciones WHERE dinámicamente
    $where_conditions = [];
    $where_conditions[] = "idcontrol = '".$control['idcontrol']."'";

    // Filtro de estado
    if (in_array('todos', $estado_filter)) {
        // No agregar filtro de estado
    } else {
        $estados_escaped = array_map(function($estado) {
            return "'" . addslashes($estado) . "'";
        }, $estado_filter);
        $where_conditions[] = "estado IN (" . implode(',', $estados_escaped) . ")";
    }

    // Filtro de participantes
    if (!empty($participantes_filter)) {
        $participantes_array = parseParticipantFilter($participantes_filter);
        if (!empty($participantes_array)) {
            $where_conditions[] = "idparticipante IN (" . implode(',', array_map('intval', $participantes_array)) . ")";
        }
    }

    // Filtro de controles - saltar este control si no está en el filtro seleccionado
    if (!empty($controles_filter) && !in_array('', $controles_filter) && !in_array($control['idcontrol'], $controles_filter)) {
        continue; // Saltar este control si no está en el filtro
    }

    // Filtro de hora desde
    if (!empty($hora_desde)) {
        $where_conditions[] = "tiempo >= '" . addslashes($hora_desde) . "'";
    }

    // Filtro de hora hasta
    if (!empty($hora_hasta)) {
        $where_conditions[] = "tiempo <= '" . addslashes($hora_hasta) . "'";
    }

    // Filtro de cronometrador
    if (!empty($cronometrador_filter)) {
        $where_conditions[] = "timer LIKE '%" . addslashes($cronometrador_filter) . "%'";
    }

    // Filtro original de idparticipante (del formulario simple)
    if (is_numeric($idparticipante) && $idparticipante >= 0) {
        $where_conditions[] = "idparticipante = $idparticipante";
    }

    $where_clause = implode(' AND ', $where_conditions);

    $lecturas_sql = FuncionesComunes::consulta_sql(
        "SELECT idlectura, idcontrol, idparticipante, tiempo, timer, estado
        FROM lecturas
        WHERE $where_clause
        ORDER BY tiempo
        ");

    while ($lectura = FuncionesComunes::array_sql($lecturas_sql)) {

        list($dia, $hora) = explode(' ', $lectura['tiempo']);
        if ($evento->largada != '0000-00-00 00:00:00.000'
            && $lectura['tiempo']
            && ($evento->largada < $lectura['tiempo'])) {
            $tiempo_listo = FuncionesComunes::convertirFinal(FuncionesComunes::segundosTranscurridos($evento->largada, $lectura['tiempo']));
        }
        echo '
        <tr>
            <td style="text-align: center;"><input type="number" name="idparticipante" value="'. $lectura['idparticipante'] .'" /></td>
            <td style="text-align: center;"><input type="text" name="tiempo" value="'. $lectura['tiempo'] .'" onchange="change_tiempo($(this), \'tiempo\')" /></td>
            <td style="text-align: center;"><input type="text" name="tiempo_listo" value="'. $tiempo_listo .'" onchange="change_tiempo($(this), \'tiempo_listo\')" /></td>
            <td style="text-align: center;">'.$lectura['timer'].'</td>
            <td style="text-align: center;">
                <select>
                    <option value="ok" '.($lectura['estado'] == 'ok' ? 'selected' : '').'>OK</option>
                    <option value="error" '.($lectura['estado'] == 'error' ? 'selected' : '').'>Error</option>
                    <option value="eliminado" '.($lectura['estado'] == 'eliminado' ? 'selected' : '').'>Eliminado</option>
                    <option value="repetido" '.($lectura['estado'] == 'repetido' ? 'selected' : '').'>Repetido</option>
                    <option value="hooked" '.($lectura['estado'] == 'hooked' ? 'selected' : '').'>Hooked</option>
                    <option value="lap" '.($lectura['estado'] == 'lap' ? 'selected' : '').'>Lap</option>
                    <option value="dnf" '.($lectura['estado'] == 'dnf' ? 'selected' : '').'>DNF</option>
                    <option value="dsq" '.($lectura['estado'] == 'dsq' ? 'selected' : '').'>DSQ</option>
                    <option value="dns" '.($lectura['estado'] == 'dns' ? 'selected' : '').'>DNS</option>
                    <option value="mss" '.($lectura['estado'] == 'mss' ? 'selected' : '').'>MSS</option>
                    <option value="otro" '.($lectura['estado'] == 'otro' ? 'selected' : '').'>Otro</option>
                </select>
            </td>
            <td style="text-align: center;">
                <input type="hidden" name="codigo" value="'. $control['codigo'] .'" />
                <input type="hidden" name="idlectura" value="'. $lectura['idlectura'] .'" />
                <input type="hidden" name="tiempo_original" value="'. $lectura['tiempo'] .'" />
                <input type="hidden" name="tiempo_listo_original" value="'. $tiempo_listo .'" />
                <button onclick="change_tiempo($(this), \'mas_seg\')">+ 1s</button>
                <button onclick="change_tiempo($(this), \'menos_seg\')">- 1s</button>
                <button onclick="ok_lectura($(this))">Modificar</button>
                <button onclick="baja_lectura($(this))">Borrar</button>
            </td>
        </tr>';

    }
}
?>
    </table>

    <?php endif; // Fin de la condición de filtros aplicados ?>

<style>
/* Estilos para los selectores de estado */
.estado-ok { background-color: #d4edda; color: #155724; }
.estado-error { background-color: #f8d7da; color: #721c24; }
.estado-eliminado { background-color: #f1f3f4; color: #5f6368; }
.estado-repetido { background-color: #fff3cd; color: #856404; }
.estado-hooked { background-color: #cce5ff; color: #004085; }
.estado-lap { background-color: #e2e3e5; color: #383d41; }
.estado-dnf { background-color: #f5c6cb; color: #721c24; }
.estado-dsq { background-color: #f5c6cb; color: #721c24; }
.estado-dns { background-color: #f5c6cb; color: #721c24; }
.estado-mss { background-color: #ffeaa7; color: #6c5ce7; }
.estado-otro { background-color: #fd79a8; color: #2d3436; }

select[onchange*="cambiar_estado"] {
    border: 1px solid #ccc;
    border-radius: 3px;
    padding: 2px 4px;
    font-size: 12px;
}
</style>

<script>
// Token CSRF para las peticiones AJAX
const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

// Función para cambiar el estado de una lectura
function cambiar_estado(selectElement, idlectura) {
    const nuevoEstado = selectElement.value;
    const estadoOriginal = selectElement.getAttribute('data-original') || selectElement.value;

    // Confirmar el cambio
    if (!confirm('¿Está seguro de cambiar el estado a "' + nuevoEstado + '"?')) {
        selectElement.value = estadoOriginal;
        return;
    }

    // Realizar petición AJAX
    fetch('/lecturas/actualizar-estado', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken
        },
        body: JSON.stringify({
            idlectura: idlectura,
            estado: nuevoEstado
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Estado actualizado correctamente');
            selectElement.setAttribute('data-original', nuevoEstado);
            // Cambiar el color del select según el estado
            selectElement.className = 'estado-' + nuevoEstado;
        } else {
            alert('Error al actualizar el estado: ' + data.message);
            selectElement.value = estadoOriginal;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error de conexión al actualizar el estado');
        selectElement.value = estadoOriginal;
    });
}

// Mejorar la usabilidad de los selectores múltiples
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar colores de los selectores de estado
    document.querySelectorAll('select[onchange*="cambiar_estado"]').forEach(function(select) {
        select.className = 'estado-' + select.value;
        select.setAttribute('data-original', select.value);
    });
    // Manejar el selector de estados
    const estadoSelect = document.getElementById('estado_filter');
    if (estadoSelect) {
        estadoSelect.addEventListener('change', function() {
            const options = Array.from(this.options);
            const todosOption = options.find(opt => opt.value === 'todos');

            if (todosOption && todosOption.selected) {
                // Si se selecciona "Todos", deseleccionar las demás opciones
                options.forEach(opt => {
                    if (opt.value !== 'todos') {
                        opt.selected = false;
                    }
                });
            } else if (this.selectedOptions.length > 0) {
                // Si se selecciona cualquier otra opción, deseleccionar "Todos"
                if (todosOption) {
                    todosOption.selected = false;
                }
            }
        });
    }

    // Manejar el selector de controles
    const controlesSelect = document.getElementById('controles_filter');
    if (controlesSelect) {
        controlesSelect.addEventListener('change', function() {
            const options = Array.from(this.options);
            const todosOption = options.find(opt => opt.value === '');

            if (todosOption && todosOption.selected) {
                // Si se selecciona "Todos", deseleccionar las demás opciones
                options.forEach(opt => {
                    if (opt.value !== '') {
                        opt.selected = false;
                    }
                });
            } else if (this.selectedOptions.length > 0) {
                // Si se selecciona cualquier otra opción, deseleccionar "Todos"
                if (todosOption) {
                    todosOption.selected = false;
                }
            }
        });
    }
});
</script>

@include('old_admin/fin')
