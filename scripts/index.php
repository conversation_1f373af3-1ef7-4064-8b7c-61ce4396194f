<?php
// Script para guardar JSON recibido en un log

// Configuración
$log_file = __DIR__ . '/../logs/json_' . date('Y-m-d') . '.log';

// Crear directorio de logs si no existe
$log_dir = dirname($log_file);
if (!is_dir($log_dir)) {
    mkdir($log_dir, 0755, true);
}

// Capturar JSON recibido
$json_data = file_get_contents('php://input');

// Solo guardar si hay datos
if (!empty($json_data)) {
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $json_data\n";

    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

// Responder
header('Content-Type: application/json');
echo json_encode(['status' => 'logged']);
?>