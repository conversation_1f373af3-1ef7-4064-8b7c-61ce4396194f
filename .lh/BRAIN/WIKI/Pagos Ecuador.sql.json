{"sourceFile": "BRAIN/WIKI/Pagos Ecuador.sql", "activeCommit": 0, "commits": [{"activePatchIndex": 7, "patches": [{"date": 1743013655201, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1743014041238, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,7 @@\n+# Ver todos los pendientes\n+\n+SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (41, 151)) AND estado_pago = 'pendiente' ORDER BY fecha;\n+\n+# Arranco seleccionando idevento\n+\n+SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (41, 151)) AND estado_pago = 'pendiente' ORDER BY fecha;\n"}, {"date": 1743014689488, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,5 +3,16 @@\n SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (41, 151)) AND estado_pago = 'pendiente' ORDER BY fecha;\n \n # Arranco seleccionando idevento\n \n-SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (41, 151)) AND estado_pago = 'pendiente' ORDER BY fecha;\n+SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (41, 151)) AND estado_pago = 'pendiente' ORDER BY fecha LIMIT 6;\n+\n+# Controlo que se deban en SaaS\n+\n+SELECT * FROM ventas WHERE idrelacion IN (2393, 2394, 2448, 2251, 2456, 2391)\n+\n+# Poner como pagado en Crono y avisar\n+\n+UPDATE eventos SET estado_pago = 'aprobado' WHERE idevento IN (2393, 2394, 2448, 2251, 2456, 2391)\n+\n+# Generar la consulta para aprobar y cargar pagos\n+\n"}, {"date": 1743014785314, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,4 +15,6 @@\n UPDATE eventos SET estado_pago = 'aprobado' WHERE idevento IN (2393, 2394, 2448, 2251, 2456, 2391)\n \n # Generar la consulta para aprobar y cargar pagos\n \n+SELECT CONCAT(\"\")\n+\n"}, {"date": 1743015087314, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,6 +15,12 @@\n UPDATE eventos SET estado_pago = 'aprobado' WHERE idevento IN (2393, 2394, 2448, 2251, 2456, 2391)\n \n # Generar la consulta para aprobar y cargar pagos\n \n-SELECT CONCAT(\"\")\n+SELECT CONCAT(\"https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=\", idevento) FROM eventos WHERE idevento IN (2393, 2394, 2448, 2251, 2456, 2391)\n \n+('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2251'),\n+('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2391'),\n+('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2393'),\n+('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2394'),\n+('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2448'),\n+('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2456');\n\\ No newline at end of file\n"}, {"date": 1743015112297, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,10 +15,12 @@\n UPDATE eventos SET estado_pago = 'aprobado' WHERE idevento IN (2393, 2394, 2448, 2251, 2456, 2391)\n \n # Generar la consulta para aprobar y cargar pagos\n \n-SELECT CONCAT(\"https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=\", idevento) FROM eventos WHERE idevento IN (2393, 2394, 2448, 2251, 2456, 2391)\n+# Por ahora cargué demás, solo hay que restar en la caja\n \n+# SELECT CONCAT(\"https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=\", idevento) FROM eventos WHERE idevento IN (2393, 2394, 2448, 2251, 2456, 2391)\n+\n ('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2251'),\n ('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2391'),\n ('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2393'),\n ('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2394'),\n"}, {"date": 1743274747349, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,11 @@\n # Ver todos los pendientes\n \n-SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (41, 151)) AND estado_pago = 'pendiente' ORDER BY fecha;\n+SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (151)) AND estado_pago = 'pendiente' ORDER BY fecha;\n \n # Arranco seleccionando idevento\n \n-SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (41, 151)) AND estado_pago = 'pendiente' ORDER BY fecha LIMIT 6;\n+SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (151)) AND estado_pago = 'pendiente' ORDER BY fecha LIMIT 6;\n \n # Controlo que se deban en SaaS\n \n SELECT * FROM ventas WHERE idrelacion IN (2393, 2394, 2448, 2251, 2456, 2391)\n"}, {"date": 1756321133399, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,28 +1,15 @@\n # Ver todos los pendientes\n \n-SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (151)) AND estado_pago = 'pendiente' ORDER BY fecha;\n+SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (151)) AND estado_pago = 'pendiente' AND eventos.estado != 'cancelado' ORDER BY fecha LIMIT 12;\n \n-# Arranco seleccionando idevento\n+# Poner como pagado en Crono y avisar\n \n-SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (151)) AND estado_pago = 'pendiente' ORDER BY fecha LIMIT 6;\n+UPDATE eventos SET estado_pago = 'aprobado' WHERE idevento IN (2503);\n \n-# Controlo que se deban en SaaS\n+# Enlaces\n \n-SELECT * FROM ventas WHERE idrelacion IN (2393, 2394, 2448, 2251, 2456, 2391)\n+https://beta.cronometrajeinstantaneo.com/inicio/eventos/2503\n \n-# Poner como pagado en Crono y avisar\n-\n-UPDATE eventos SET estado_pago = 'aprobado' WHERE idevento IN (2393, 2394, 2448, 2251, 2456, 2391)\n-\n-# Generar la consulta para aprobar y cargar pagos\n-\n # Por ahora cargué demás, solo hay que restar en la caja\n \n-# SELECT CONCAT(\"https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=\", idevento) FROM eventos WHERE idevento IN (2393, 2394, 2448, 2251, 2456, 2391)\n-\n-('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2251'),\n\\ No newline at end of file\n-('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2391'),\n-('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2393'),\n-('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2394'),\n-('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2448'),\n-('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2456');\n+https://scripts.saasargentina.com/?script=1&iue=MTVvtWMaVPdEGDpcsyU3&api_secret=i8eOimYHPv9bjPzF8de7q2OaSG2d0UYq&a=nuevo_pago_completo&idcliente=75&nombre=Ecuador&<EMAIL>&producto=representante&precio=50&descuento=0&total=50&moneda=2&pago=50&formapago=payoneer&observacion=&fecha=2025-04-12%2000:00:00&idservicio=2590\n"}], "date": 1743013655201, "name": "Commit-0", "content": ""}]}