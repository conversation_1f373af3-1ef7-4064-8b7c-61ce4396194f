<?php

namespace Inscripciones;

use PDO;

class Aprobacion
{
    private $db;
    private $inscripcion;
    private $evento;
    private $logger;

    public function __construct(Inscripcion $inscripcion)
    {
        $this->inscripcion = $inscripcion;
        $this->evento = $inscripcion->getEvento();
        $this->db = Db::db();
        $this->logger = new Logger();
    }

    public function aprobarFicha()
    {
        // Obtener estados anteriores
        $estados_anteriores = $this->obtenerEstados();

        $this->db->beginTransaction();

        try {
            // Actualizar estado_ficha
            $stmt = $this->db->prepare(
                "UPDATE participantes
                SET estado_ficha = 'aprobado'
                WHERE idinscripcion = :idinscripcion
                AND idevento = :idevento"
            );
            $stmt->bindValue(':idinscripcion', $this->inscripcion->idinscripcion(), PDO::PARAM_INT);
            $stmt->bindValue(':idevento', $this->evento->idevento, PDO::PARAM_INT);
            $stmt->execute();

            // Si es un equipo, actualizar también los participantes
            if ($this->inscripcion->getRoute()->participantesxequipo() > 1) {
                $stmt = $this->db->prepare(
                    "UPDATE participantes
                    SET estado_ficha = 'aprobado'
                    WHERE idequipo = :idequipo
                    AND idevento = :idevento"
                );
                $stmt->bindValue(':idequipo', $this->inscripcion->idinscripcion(), PDO::PARAM_INT);
                $stmt->bindValue(':idevento', $this->evento->idevento, PDO::PARAM_INT);
                $stmt->execute();
            }

            // Verificar si debe cambiar a inscripto (solo si ambos están aprobados)
            $cambio_a_inscripto = $this->verificarCambioAInscriptoPorFicha();

            $this->db->commit();

            // Obtener estados posteriores
            $estados_posteriores = $this->obtenerEstados();

            // Log de aprobación exitosa
            $this->logger->log('APROBAR_FICHA', [
                $this->evento->idevento,
                $this->inscripcion->idinscripcion(),
                $this->evento->codigo,
                $this->inscripcion->nombre,
                'EXITOSO',
                $estados_anteriores ? $estados_anteriores['estado_ficha'] : 'N/A',
                $estados_posteriores ? $estados_posteriores['estado_ficha'] : 'N/A',
                $estados_posteriores && $estados_posteriores['estado'] == 'inscripto' ? 'AUTO_INSCRIPTO' : 'NO_AUTO_INSCRIPTO'
            ], 'aprobaciones');

            // Log de cambio automático si ocurrió
            if ($cambio_a_inscripto) {
                $this->logger->log('AUTO_INSCRIPTO', [
                    $this->evento->idevento,
                    $this->inscripcion->idinscripcion(),
                    $this->evento->codigo,
                    $this->inscripcion->nombre,
                    'EXITOSO',
                    'preinscripto',
                    'inscripto',
                    $estados_posteriores['estado_ficha'],
                    $estados_posteriores['estado_pago']
                ], 'aprobaciones');
            }

            return true;
        } catch (\Exception $e) {
            $this->db->rollBack();

            // Log de error
            $this->logger->log('ERROR_APROBACION', [
                $this->evento->idevento,
                $this->inscripcion->idinscripcion(),
                $this->evento->codigo,
                $this->inscripcion->nombre,
                'FICHA',
                'ERROR',
                $e->getMessage()
            ], 'aprobaciones');

            return false;
        }
    }

    public function aprobarPago()
    {
        // Obtener estados anteriores
        $estados_anteriores = $this->obtenerEstados();

        $this->db->beginTransaction();

        try {
            // Actualizar estado_pago
            $stmt = $this->db->prepare(
                "UPDATE participantes
                SET estado_pago = 'aprobado'
                WHERE idinscripcion = :idinscripcion
                AND idevento = :idevento"
            );
            $stmt->bindValue(':idinscripcion', $this->inscripcion->idinscripcion(), PDO::PARAM_INT);
            $stmt->bindValue(':idevento', $this->evento->idevento, PDO::PARAM_INT);
            $stmt->execute();

            // Si es un equipo, actualizar también los participantes
            if ($this->inscripcion->getRoute()->participantesxequipo() > 1) {
                $stmt = $this->db->prepare(
                    "UPDATE participantes
                    SET estado_pago = 'aprobado'
                    WHERE idequipo = :idequipo
                    AND idevento = :idevento"
                );
                $stmt->bindValue(':idequipo', $this->inscripcion->idinscripcion(), PDO::PARAM_INT);
                $stmt->bindValue(':idevento', $this->evento->idevento, PDO::PARAM_INT);
                $stmt->execute();
            }

            // Cambiar automáticamente a inscripto cuando se aprueba el pago
            $cambio_a_inscripto = $this->verificarCambioAInscriptoPorPago();

            $this->db->commit();

            // Obtener estados posteriores
            $estados_posteriores = $this->obtenerEstados();

            // Log de aprobación exitosa
            $this->logger->log('APROBAR_PAGO', [
                $this->evento->idevento,
                $this->inscripcion->idinscripcion(),
                $this->evento->codigo,
                $this->inscripcion->nombre,
                'EXITOSO',
                $estados_anteriores ? $estados_anteriores['estado_pago'] : 'N/A',
                $estados_posteriores ? $estados_posteriores['estado_pago'] : 'N/A',
                $estados_posteriores && $estados_posteriores['estado'] == 'inscripto' ? 'AUTO_INSCRIPTO' : 'NO_AUTO_INSCRIPTO'
            ], 'aprobaciones');

            // Log de cambio automático si ocurrió
            if ($cambio_a_inscripto) {
                $this->logger->log('AUTO_INSCRIPTO', [
                    $this->evento->idevento,
                    $this->inscripcion->idinscripcion(),
                    $this->evento->codigo,
                    $this->inscripcion->nombre,
                    'EXITOSO',
                    'preinscripto',
                    'inscripto',
                    $estados_posteriores['estado_ficha'],
                    $estados_posteriores['estado_pago']
                ], 'aprobaciones');
            }

            return true;
        } catch (\Exception $e) {
            $this->db->rollBack();

            // Log de error
            $this->logger->log('ERROR_APROBACION', [
                $this->evento->idevento,
                $this->inscripcion->idinscripcion(),
                $this->evento->codigo,
                $this->inscripcion->nombre,
                'PAGO',
                'ERROR',
                $e->getMessage()
            ], 'aprobaciones');

            return false;
        }
    }

    private function verificarCambioAInscriptoPorPago()
    {
        // Obtener el estado actual
        $stmt = $this->db->prepare(
            "SELECT estado, estado_ficha, estado_pago
            FROM participantes
            WHERE idinscripcion = :idinscripcion
            AND idevento = :idevento"
        );
        $stmt->bindValue(':idinscripcion', $this->inscripcion->idinscripcion(), PDO::PARAM_INT);
        $stmt->bindValue(':idevento', $this->evento->idevento, PDO::PARAM_INT);
        $stmt->execute();
        $participante = $stmt->fetch(PDO::FETCH_ASSOC);

        // Si está preinscripto y el pago está aprobado, cambiar a inscripto (independientemente del estado de la ficha)
        if ($participante &&
            $participante['estado'] == 'preinscripto' &&
            $participante['estado_pago'] == 'aprobado') {

            // Ejecutar directamente las consultas SQL en lugar de usar update()
            $update = [];
            $update[] = "estado = 'inscripto'";
            $update[] = "fechapago = NOW()";

            // Asignar número de participante si corresponde
            if ($this->evento->auto_numeracion && !$this->inscripcion->idparticipante) {
                $update[] = "idparticipante = '" . $this->inscripcion->siguienteIdparticipante() . "'";
            }

            $stmt = $this->db->prepare(
                "UPDATE participantes
                SET " . implode(', ', $update) . "
                WHERE idinscripcion = :idinscripcion
                AND idevento = :idevento"
            );
            $stmt->bindValue(':idinscripcion', $this->inscripcion->idinscripcion(), PDO::PARAM_INT);
            $stmt->bindValue(':idevento', $this->evento->idevento, PDO::PARAM_INT);
            $stmt->execute();

            // Si es un equipo, actualizar también los participantes
            if ($this->inscripcion->getRoute()->participantesxequipo() > 1) {
                $stmt = $this->db->prepare(
                    "UPDATE participantes
                    SET estado = 'inscripto'
                    WHERE idequipo = :idequipo
                    AND idevento = :idevento"
                );
                $stmt->bindValue(':idequipo', $this->inscripcion->idinscripcion(), PDO::PARAM_INT);
                $stmt->bindValue(':idevento', $this->evento->idevento, PDO::PARAM_INT);
                $stmt->execute();
            }

            // Enviar notificación de inscripción confirmada
            if ($this->evento->auto_mail && $this->evento->mail_inscripto) {
                $this->inscripcion->notificar('inscripto');
            }

            return true;
        }

        return false;
    }

    private function verificarCambioAInscriptoPorFicha()
    {
        // Obtener el estado actual
        $stmt = $this->db->prepare(
            "SELECT estado, estado_ficha, estado_pago
            FROM participantes
            WHERE idinscripcion = :idinscripcion
            AND idevento = :idevento"
        );
        $stmt->bindValue(':idinscripcion', $this->inscripcion->idinscripcion(), PDO::PARAM_INT);
        $stmt->bindValue(':idevento', $this->evento->idevento, PDO::PARAM_INT);
        $stmt->execute();
        $participante = $stmt->fetch(PDO::FETCH_ASSOC);

        // Si está preinscripto y ambos están aprobados, cambiar a inscripto
        if ($participante &&
            $participante['estado'] == 'preinscripto' &&
            $participante['estado_ficha'] == 'aprobado' &&
            $participante['estado_pago'] == 'aprobado') {

            // Ejecutar directamente las consultas SQL en lugar de usar update()
            $update = [];
            $update[] = "estado = 'inscripto'";
            $update[] = "fechapago = NOW()";

            // Asignar número de participante si corresponde
            if ($this->evento->auto_numeracion && !$this->inscripcion->idparticipante) {
                $update[] = "idparticipante = '" . $this->inscripcion->siguienteIdparticipante() . "'";
            }

            $stmt = $this->db->prepare(
                "UPDATE participantes
                SET " . implode(', ', $update) . "
                WHERE idinscripcion = :idinscripcion
                AND idevento = :idevento"
            );
            $stmt->bindValue(':idinscripcion', $this->inscripcion->idinscripcion(), PDO::PARAM_INT);
            $stmt->bindValue(':idevento', $this->evento->idevento, PDO::PARAM_INT);
            $stmt->execute();

            // Si es un equipo, actualizar también los participantes
            if ($this->inscripcion->getRoute()->participantesxequipo() > 1) {
                $stmt = $this->db->prepare(
                    "UPDATE participantes
                    SET estado = 'inscripto'
                    WHERE idequipo = :idequipo
                    AND idevento = :idevento"
                );
                $stmt->bindValue(':idequipo', $this->inscripcion->idinscripcion(), PDO::PARAM_INT);
                $stmt->bindValue(':idevento', $this->evento->idevento, PDO::PARAM_INT);
                $stmt->execute();
            }

            // Enviar notificación de inscripción confirmada
            if ($this->evento->auto_mail && $this->evento->mail_inscripto) {
                $this->inscripcion->notificar('inscripto');
            }

            return true;
        }

        return false;
    }

    // Mantener la función original para compatibilidad
    private function verificarCambioAInscripto()
    {
        return $this->verificarCambioAInscriptoPorFicha();
    }

    public function obtenerEstados()
    {
        $stmt = $this->db->prepare(
            "SELECT estado, estado_ficha, estado_pago
            FROM participantes
            WHERE idinscripcion = :idinscripcion
            AND idevento = :idevento"
        );
        $stmt->bindValue(':idinscripcion', $this->inscripcion->idinscripcion(), PDO::PARAM_INT);
        $stmt->bindValue(':idevento', $this->evento->idevento, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}