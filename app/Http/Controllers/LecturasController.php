<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Log;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use Google\Cloud\Storage\StorageClient;

use App\Helpers\FuncionesComunes;
use App\Jobs\ProcesarLectura;

use App\Models\Eventos;
use App\Models\Participantes;
use App\Models\Lecturas;
use App\Models\Controles;
use App\Models\Tags;

use App\Exports\LecturasExport;
use App\Imports\LecturasImport;

class LecturasController extends Controller
{
    private $admin;
    private $view = 'lecturas';
    private $filename;
    private $folder;

    protected $idlectura;
    protected $idparticipante;
    protected $codigo;
    protected $tagID;
    protected $tiempo;

    public function index()
    {
        $this->admin = new AdminController();

        $idevento = session('idevento');
        if (!is_numeric($idevento) || $idevento <= 0)
            return redirect('historial');

        $config_header = $this->admin->config_header($idevento);

        return view($this->view, [
            'site'          => 'crono',
            'datos'         => [],
            'idevento'      => $idevento,
            'enlaces_config'=> $config_header['enlaces_config'],
            'etapas'        => $config_header['etapas'],
            'cantidades'    => $config_header['cantidades'],
            'evento'        => $config_header['evento'],
            'pasos'         => "",
            'categorias'    => "",
            'categorias_all'=> "",
            'formasdepago'  => "",
            'backups'       => $this->backups(),
        ]);
    }

    public function store(Request $request)
    {
        $read = json_decode($request->getContent(), true);

        // COMPATIBILIDAD TEMPORAL CON EL STREAMING Y LA APP 2.X
        if (isset($read['idlectura']) && !isset($request->uuid)) {
            $idlectura = $read['idlectura'];
            $lectura = Lecturas::findOrFail($idlectura);
            ProcesarLectura::dispatch($lectura)->onQueue('tiempos');
            return json_encode($idlectura);
        }

        FuncionesComunes::log('api', $read);

        // Validar datos
        $this->validate($request, [
            'uuid'   => 'uuid',
            'codigo' => 'required|size:4',
            'idparticipante' => 'required|integer',
            'tiempo' => 'required|date_format:Y-m-d H:i:s.v',
        ]);

        $control = Controles::select('controles.nombre', 'controles.idcontrol', 'controles.idetapa', 'controles.codigo', 'controles.tipo', 'controles.idevento')
            ->where('controles.codigo', mb_strtoupper($request->codigo))
            ->first();

        if (!$control || !$control->idevento) {
            return response()->json([
                'error' => true,
                'errors' => ['codigo' => ['Código de control no válido']],
                'resultado' => 'CODIGO NO ENCONTRADO',
            ], 400);
        }

        $evento = Eventos::select('eventos.*', 'config_cronometraje.*', 'config_vivo.streaming AS streaming_live', 'config_organizacion.tipo_nombre')
            ->join('config_cronometraje', 'eventos.idevento', '=', 'config_cronometraje.idevento')
            ->join('config_vivo', 'eventos.idevento', '=', 'config_vivo.idevento')
            ->join('config_organizacion', 'eventos.idevento', '=', 'config_organizacion.idevento')
            ->where('eventos.idevento', $control->idevento)
            ->first();

        if (!$evento) {
            return response()->json([
                'error' => true,
                'errors' => ['codigo' => ['Evento no encontrado']],
                'resultado' => 'EVENTO NO ENCONTRADO',
            ], 400);
        }

        if ($evento->sync_apps == 0 and false) {
            return response()->json([
                'error' => true,
                'errors' => ['evento' => ['Evento no activado para sincronización']],
                'resultado' => 'APPS DESACTIVADAS',
            ], 400);
        }


        // PARCHE PARA MISMO CONTROL PARA LARGADA Y LLEGADA
        if ($request->idparticipante && in_array($control->idcontrol, [8, 15294, 15296, 15298, 15300,
            16419, 16421, 16423, 16425, 16427, 16491, 16493, 16495, 16497, // Pumptrack Chile
            21026, 21028, 21030, 21032, 21034, 21036, 21057, 21059, 21061, 21063, 21065, 21067, 21069, 21071, 21073, 21075, 21077, 21079, 21081, 21083, 21085, 21087, 21089, 21091, 21093, 21095, 21097, 21099, 21101, 21103, 21105, 21107, 21109, 21111, 21113, 21115, // RFC Colombia
        ])) {

            // Ver si existe un registro lecturas con el idparticipante y el idcontrol AND (estado = 'ok' OR estado = 'repetido')
            $lecturaExistente = Lecturas::where('idparticipante', $request->idparticipante)
                ->where('idcontrol', $control->idcontrol)
                ->whereIn('estado', ['ok', 'repetido'])
                ->first();

            if ($lecturaExistente) {
                if ($lecturaExistente->tiempo > $request->tiempo) {
                    // El registro existente tiene tiempo mayor, lo movemos al siguiente control
                    $lecturaExistente->idcontrol = $lecturaExistente->idcontrol + 1;
                    $lecturaExistente->save();
                } else {
                    // El nuevo registro tiene tiempo mayor o igual, lo movemos al siguiente control
                    $control->idcontrol = $control->idcontrol + 1;
                }
            }
        }
        // PARCHE PARA MISMO CONTROL PARA LARGADA Y LLEGADA

        // PARCHE PARA SPRINT (SUMA 2 CONTROLES)
        if ($request->idparticipante && in_array($control->idevento, [2313])) {

            while (Lecturas::where('idparticipante', $request->idparticipante)
                    ->where('idcontrol', $control->idcontrol)
                    ->where('uuid', '!=', $request->uuid)
                    ->whereIn('estado', ['ok', 'repetido'])
                    ->count()) {
                $control->idcontrol = $control->idcontrol + 4;
            }

        }
        // PARCHE PARA SPRINT (SUMA 2 CONTROLES)

        // PARCHE MISMO CONTROL ESPECÍFICO
        $temp_cambio_control = [
            17867 => 17869,
            17863 => 17865,
            17865 => 17880,
        ];
        if ($request->idparticipante
            && in_array($control->idevento, [2456])
            && key_exists($control->idcontrol, $temp_cambio_control)) {
            while (Lecturas::where('idparticipante', $request->idparticipante)
                    ->where('idcontrol', $control->idcontrol)
                    ->where('uuid', '!=', $request->uuid)
                    ->whereIn('estado', ['ok', 'repetido'])
                    ->count()) {
                $control->idcontrol = $temp_cambio_control[$control->idcontrol];
            }
        }
        // PARCHE MISMO CONTROL ESPECÍFICO


        // Creo o actualizo lectura
        list($vueltas, $repetido) = [0, 0];
        // Deshabilitado por ahora
        if ($evento->idevento == 1519)
            list($vueltas, $repetido) = $this->calcular_vueltas($request->uuid, $control->idcontrol, $request->idparticipante, $request->tiempo);
        Lecturas::upsert([
            'uuid' => $request->uuid,
            'idevento' => $evento->idevento,
            'idcontrol' => $control->idcontrol,
            'estado' => $request->state == 'enviar-baja' ? 'eliminado' : ($repetido ? 'repetido' : 'ok'),
            'tiempo' => $request->tiempo,
            'idparticipante' => $request->idparticipante,
            'tipo' => $request->tipo,
            'timer' => $request->timer,
            'vuelta' => $vueltas,
        ], ['uuid']);

        $lectura = Lecturas::where('uuid', $request->uuid)->first();

        // Agrego participante
        $participante = $lectura->idparticipante > 0
            ? Participantes::where('idevento', $evento->idevento)
                ->where('idparticipante', $lectura->idparticipante)
                ->where('estado', '!=', 'eliminado')
                ->first()
            : null;

        if ($participante)
            $lectura->nombre = $participante->nombre
                .($evento->tipo_nombre == 'Nombre y Apellido separados' ? ' '.$participante->apellido : '');
        else if ($lectura->idparticipante == 0)
            $lectura->idparticipante = 'FALTA';

        // Queue del vivo
            if ($evento->vivo && $evento->streaming_live
            && in_array($control->tipo, ['largada', 'final'])
            && $participante
            && $lectura->idparticipante > 0) {
            ProcesarLectura::dispatch($lectura)->onQueue('tiempos');
        }

        // Agrego resultado
        if (is_null($evento->largada) || $evento->largada == '0000-00-00 00:00:00.000')
            $lectura->resultado = 'NO - EVENTO SIN COMENZAR';

        else if (is_null($evento->terminada) || $evento->terminada != '0000-00-00 00:00:00.000')
            $lectura->resultado = 'NO - EVENTO TERMINADO';

        else if (is_null($participante))
            $lectura->resultado = 'NO existe';

        else if ($participante && $repetido)
            $lectura->resultado = 'NO repetido '.$repetido.' veces';

        else
            $lectura->resultado = 'OK';

        $lectura->hora = $request->hora;
        $lectura->tiempo_listo = FuncionesComunes::convertirFinal(
            FuncionesComunes::segundosTranscurridos($evento->largada, $request->tiempo));


    // TODO: que funcione con datos extras en el buscador

    // TODO: que funcione con tagID
    /*
        if (!$this->idparticipante && $this->tagID) {
            $this->idparticipante = Participantes::whereRaw("idinscripcion =  (SELECT idinscripcion FROM tags
                                    WHERE tagID = '{$this->tagID}'
                                    AND idevento = '{$mobile['idevento']}'
                                LIMIT 1)");

            if ($this->idparticipante->count())
                $this->idparticipante = $this->idparticipante->get()->first()->nombre;
            else
                $this->idparticipante = 0;

        }
    */

        FuncionesComunes::cache($evento->codigo);
        FuncionesComunes::log('api', $lectura);

        return response()->json($lectura);
    }

    private function backups()
    {
        $this->admin = new AdminController();
        $this->filename = 'lecturas_'.session('idevento').'_'.date('Y-m-d_H-i-s').'.xlsx';
        $this->folder = session('idevento');

        $bucket = \Storage::disk('gcs');
        $backups = [];
        foreach ($bucket->files($this->folder) as $backup) {
            if (strpos($backup, 'lecturas_'))
                $backups[] = [
                    'nombre' => str_replace([$this->folder.'/'], [''], $backup),
                    'href' => $bucket->url($backup)
                ];
        }
        return $backups;
    }

    public function backup()
    {
        $this->admin = new AdminController();
        $this->filename = 'lecturas_'.session('idevento').'_'.date('Y-m-d_H-i-s').'.xlsx';
        $this->folder = session('idevento');

        Excel::store(new LecturasExport, $this->filename);
        $path = storage_path().'/app/';

        $bucket = \Storage::disk('gcs');
        $backup = $bucket->putFileAs($this->folder, $path.$this->filename, $this->filename, 'public');

        if ($backup == $this->folder.'/'.$this->filename) {
            return redirect()->back()->with('success', 'BackUp generado correctamente');
        }

        return redirect()->back()->with('error', 'No se pudo realizar el BackUp');
    }

    public function exportar()
    {
        $this->admin = new AdminController();
        $this->filename = 'lecturas_'.session('idevento').'_'.date('Y-m-d_H-i-s').'.xlsx';
        $this->folder = session('idevento');

        return Excel::download(new LecturasExport, $this->filename);
    }

    public function importar(Request $request)
    {
        $this->admin = new AdminController();
        $this->filename = 'lecturas_'.session('idevento').'_'.date('Y-m-d_H-i-s').'.xlsx';
        $this->folder = session('idevento');

        if (!$request->file())
            return back()->with('error', 'Por favor seleccione un archivo');

        $fileName = $request->file->getClientOriginalName();
        $archivo = $request->file('file');

        if (!$archivo)
            return back()->with('error', 'Por favor seleccione un archivo');

        $request->validate([
                'file' => 'required|mimes:xlsx,xls|max:2048'
            ],
            [
                'file.required'=> 'Por favor seleccione un archivo',
                'file.mimes'=> 'El archivo debe ser del tipo :values',
                'file.max'=> 'El archivo debe tener como máximo :max kb',
            ]
        );

        try {
            Excel::import(new LecturasImport, $archivo);

        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {

            $failures = $e->failures();
            foreach ($failures as $failure) {
                $failure->row(); // row that went wrong
                $failure->attribute(); // either heading key (if using heading row concern) or column index
                $failure->errors(); // Actual error messages from Laravel validator
                $failure->values(); // The values of the row that has failed.
            }

            return back()->withErrors($failures);

        } catch (\Exception $e) {
            if (app()->bound('sentry')) {
                app('sentry')->captureException($e);
            }
            return back()->withError('El archivo contiene algún error y no puede ser importado: '.$e->getMessage());

        } catch (\Error $e) {
            if (app()->bound('sentry')) {
                app('sentry')->captureException($e);
            }
            return back()->withError('El archivo contiene algún error y no puede ser importado: '.$e->getMessage());
        }

        FuncionesComunes::cache(session('codigo'));

        return redirect()->back()
            ->with('success', 'Importación completada correctamente')
            ->with('file', $fileName);
    }

    public function vaciar()
    {
        $this->admin = new AdminController();
        $this->filename = 'lecturas_'.session('idevento').'_'.date('Y-m-d_H-i-s').'.xlsx';
        $this->folder = session('idevento');

        if (!$this->backups())
            return redirect()->back()->with('error', 'No se puede vaciar las lecturas sin un BackUp previo');

        if (!Lecturas::where('idevento', session('idevento'))
            ->where('estado', '!=', 'eliminado')
            ->update(['estado' => 'eliminado']))
            return redirect()->back()->with('error', 'No se pudo vaciar las lecturas, o no había lecturas para vaciar');

        return redirect()->back()->with('success', 'Todas las lecturas fueron eliminadas correctamente');
    }

    private function calcular_vueltas($uuid, $idcontrol, $idparticipante, $tiempo)
    {
        if ($idparticipante <= 0)
            return [0, 0];

        $cant_vueltas = $cant_repetido = 0;
        $lecturas_anteriores = Lecturas::select('idlectura', 'tiempo')
            ->where('idcontrol', $idcontrol)
            ->where('estado', '!=', 'eliminado')
            ->where('idparticipante', $idparticipante)
            ->where('uuid', '!=', $uuid)
            ->orderBy('tiempo')
            ->get();

        $ultima_vuelta = 0;
        foreach ($lecturas_anteriores as $lectura_anterior) {
            // var_dump($lectura_anterior);

            if (strtotime($tiempo) - $ultima_vuelta < 30000) {
                $cant_repetido++;

            } else {
                $cant_vueltas++;
                $cant_repetido = 0;
                $ultima_vuelta = strtotime($lectura_anterior->tiempo);
            }
        }

        if (strtotime($tiempo) - $ultima_vuelta < 30) {
            $cant_repetido++;

        } else {
            $cant_vueltas++;
            $cant_repetido = 0;
        }

        return [$cant_vueltas, $cant_repetido];
    }
}
